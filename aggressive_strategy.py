# -*- coding: utf-8 -*-
"""
激进版择时量化投资策略
基于创业板ETF(159915)技术指标进行择时，简化版本：
- 激活期：一次性买入固定金额的ACTIVE_FUND_CODE
- 沉睡期：全部卖出ACTIVE_FUND_CODE，买入等值的SLEEPING_FUND_CODE
- 每个期间只交易一次，避免重复操作
"""

import sqlite3
import datetime
import json
import math
import time
import numpy as np
from typing import Dict, List, Tuple, Optional
import traceback
import uuid
from enum import Enum

# ==================== 任务队列枚举定义 ====================

class TaskType(Enum):
    """任务类型枚举"""
    SELL_SLEEPING_FUND = "SELL_SLEEPING_FUND"      # 卖出沉睡期基金
    SELL_ACTIVE_FUND = "SELL_ACTIVE_FUND"          # 卖出激活期基金
    BUY_ACTIVE_FUND_CASH = "BUY_ACTIVE_FUND_CASH"  # 现金买入激活期基金
    BUY_ACTIVE_FUND_MARGIN = "BUY_ACTIVE_FUND_MARGIN"  # 融资买入激活期基金
    BUY_SLEEPING_FUND = "BUY_SLEEPING_FUND"        # 买入沉睡期基金（仅现金）

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "PENDING"                    # 待执行
    EXECUTING = "EXECUTING"                # 执行中
    WAITING_CALLBACK = "WAITING_CALLBACK"  # 等待回调
    COMPLETED = "COMPLETED"                # 已完成
    FAILED = "FAILED"                      # 失败
    TIMEOUT = "TIMEOUT"                    # 超时

class SnapshotPoint(Enum):
    """账户快照时点枚举"""
    BEFORE_SELL = "BEFORE_SELL"           # 卖出前
    AFTER_SELL = "AFTER_SELL"             # 卖出后
    AFTER_BUY_CASH = "AFTER_BUY_CASH"     # 现金买入后
    AFTER_BUY_MARGIN = "AFTER_BUY_MARGIN" # 融资买入后

# ==================== 策略参数配置 ====================

#############################################################################
### 用户可以配置的参数 BEGIN
#############################################################################

# 基金代码配置
SLEEPING_FUND_CODE = "510720.SH"    # 沉睡期基金：国泰上证国有企业红利ETF
ACTIVE_FUND_CODE = "159967.SZ"      # 激活期基金：创业板ETF
SIGNAL_FUND_CODE = "159915.SZ"      # 信号检测基金：创业板ETF

# 激进版核心参数
AGGRESSIVE_INVESTMENT_AMOUNT = 10000  # 激进版固定投资金额（元）

# 技术指标参数(季线)
EMA_PERIOD = 35                 # EMA参数：默认35，可配置2-500
BOTTOM_RATIO = 0.85             # 底部相对比例：默认0.85
TOP_RATIO = 1.90                # 顶部相对比例：默认1.90

# 交易时点控制参数
TRADE_TIME_CONTROL = "144500"   # 交易时点控制：HHmmss格式，如144500代表14:45:00
ENABLE_TIME_CONTROL = True      # 是否启用时点控制（实盘模式有效，回测模式忽略）

# 交易账户信息
ACCOUNT_ID = "************"     # 设置交易账户信息（只能支持一个）
ACCOUNT_TYPE = "CREDIT"         # 交易账户类型，手动设置
COMMISSION_FEE_RATE = 0.0001   # 佣金费率（万分之1）
COMMISSION_FEE_MIN = 5         # 最低交易佣金（元）
SELL_TAX_RATE = 0.0005          # 印花税率（万分之5，仅卖出）
TRANSFER_FEE_RATE = 0.00002    # 过户费率（万分之0.2，仅上海）

#############################################################################
### 用户可以配置的参数 END
#############################################################################

# 技术指标参数
EMA_DETECTION_CYCLE = "1q"      # EMA检测周期：季(1q)

# 信号过滤参数
BUY_SIGNAL_FILTER_PERIODS = 8   # 买入信号过滤周期：8个周期内不重复
SELL_SIGNAL_FILTER_PERIODS = 10 # 卖出信号过滤周期：10个周期内不重复

# 交易执行参数
MAX_RETRY_COUNT = 3             # 最大重试次数
RETRY_INTERVAL = 300            # 重试间隔（秒）
MIN_TRADE_SHARES = 100          # 最小交易股数（必须是100的倍数）

# 数据库配置
DATABASE_PATH = "gy_aggressive_trading.db"   # 数据库文件路径

# 回测模式配置
IS_BACKTEST_MODE = False       # True: 回测模式（不真实下单）, False: 实盘模式

# ==================== 平台兼容性检查 ====================
# iQuant平台函数可用性检查
IQUANT_FUNCTIONS_AVAILABLE = False
try:
    # 检查是否在iQuant环境中
    passorder
    get_trade_detail_data
    IQUANT_FUNCTIONS_AVAILABLE = True
    print("iQuant平台函数可用")
except NameError:
    print("非iQuant环境，将使用模拟函数")
    IQUANT_FUNCTIONS_AVAILABLE = False

    # 定义模拟函数
    def passorder(*args, **kwargs):
        """模拟passorder函数"""
        print(f"[模拟] passorder调用: args={args}")
        return "MOCK_ORDER_ID"

    def get_trade_detail_data(*args, **kwargs):
        """模拟get_trade_detail_data函数"""
        print(f"[模拟] get_trade_detail_data调用: args={args}")
        return {}

# ==================== 全局变量 ====================
g_strategy_status = None        # 策略状态
g_db_connection = None          # 数据库连接
g_current_bar_time = None       # 当前K线时间（回测时使用）

# 任务队列相关全局变量
g_trade_task_queue = None       # 交易任务队列
g_trade_task_executor = None    # 交易任务执行器
g_trade_task_callback_handler = None  # 交易任务回调处理器

# ==================== 回测适配函数 ====================

def is_backtest_mode(ContextInfo=None) -> bool:
    """判断是否为回测模式"""
    return IS_BACKTEST_MODE

def get_current_time(ContextInfo) -> datetime.datetime:
    """获取当前时间（回测适配）"""
    global g_current_bar_time
    
    try:
        if is_backtest_mode(ContextInfo):
            # 回测模式：使用当前K线的时间
            try:
                current_bar_timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
                if current_bar_timestamp:
                    g_current_bar_time = datetime.datetime.fromtimestamp(current_bar_timestamp/1000)
            except:
                print('获取K线时间失败，将会返回global的bartime')
                pass
            
            if g_current_bar_time:
                return g_current_bar_time
            else:
                return datetime.datetime.now()
        else:
            # 实盘模式：使用系统当前时间
            current_time = datetime.datetime.now()
            g_current_bar_time = current_time
            return current_time
            
    except Exception as e:
        log_message("WARNING", "时间获取", f"获取当前时间失败：{str(e)}", None, ContextInfo)
        current_time = datetime.datetime.now()
        g_current_bar_time = current_time
        return current_time

def get_current_time_str(ContextInfo) -> str:
    """获取当前时间字符串（回测适配）"""
    return get_current_time(ContextInfo).strftime("%Y-%m-%d %H:%M:%S")

def should_execute_strategy(ContextInfo) -> bool:
    """判断是否应该执行策略逻辑（回测适配）"""
    try:
        if is_backtest_mode(ContextInfo):
            return True
        else:
            return ContextInfo.is_last_bar()
    except Exception as e:
        log_message("WARNING", "策略执行判断", f"判断是否执行策略失败：{str(e)}", None, ContextInfo)
        return False

# ==================== 主要策略函数 ====================

def init(ContextInfo):
    """iQuant策略初始化函数"""
    try:
        print("=" * 60)
        print("激进版择时量化投资策略 - 初始化开始")
        print("=" * 60)

        ContextInfo.set_universe([ACTIVE_FUND_CODE, SLEEPING_FUND_CODE, SIGNAL_FUND_CODE])

        # 1. 初始化数据库
        print("正在初始化数据库...")
        init_database()
        print("数据库初始化完成！")

        # 2. 加载策略状态
        print("正在加载策略状态...")
        load_strategy_status()
        print("策略状态加载完成！")

        # 3. 初始化任务队列
        print("正在初始化任务队列...")
        init_task_queue()
        print("任务队列初始化完成！")

        # 3. 设置账户信息
        ContextInfo.set_account(ACCOUNT_ID)
        print(f"已设置交易账户:{ACCOUNT_ID}")

        # 4. 显示策略配置信息
        print("\n激进版策略配置信息：")
        print(f"  沉睡期基金：{SLEEPING_FUND_CODE} (红利国企ETF)")
        print(f"  激活期基金：{ACTIVE_FUND_CODE} (创业板ETF)")
        print(f"  信号检测基金：{SIGNAL_FUND_CODE}")
        print(f"  固定投资金额：{AGGRESSIVE_INVESTMENT_AMOUNT:,}元")
        print(f"  EMA检测周期：{EMA_DETECTION_CYCLE}")
        print(f"  EMA参数：{EMA_PERIOD}")
        print(f"  底部比例：{BOTTOM_RATIO}")
        print(f"  顶部比例：{TOP_RATIO}")

        # 5. 下载历史数据
        current_date_str = datetime.datetime.now().strftime('%Y%m%d')
        print(f'即将下载 {SIGNAL_FUND_CODE} 的日线数据...')
        down_history_data(SIGNAL_FUND_CODE, '1d', '********', current_date_str)
        print(f'{SIGNAL_FUND_CODE} 的日线数据下载完成！')

        if ACTIVE_FUND_CODE != SIGNAL_FUND_CODE:
            print(f'即将下载 {ACTIVE_FUND_CODE} 的日线数据...')
            down_history_data(ACTIVE_FUND_CODE, '1d', '********', current_date_str)
            print(f'{ACTIVE_FUND_CODE} 的日线数据下载完成！')

        # 记录初始化日志
        log_message("INFO", "策略初始化", "激进版策略初始化成功", None, ContextInfo)

        print("\n" + "=" * 60)
        print("激进版择时策略初始化完成！")
        print("=" * 60)

    except Exception as e:
        error_msg = f"激进版策略初始化失败：{str(e)}"
        print(f"\n❌ {error_msg}")
        print("=" * 60)
        log_message("ERROR", "策略初始化", error_msg, None, ContextInfo)
        raise e

def handlebar(ContextInfo):
    """iQuant主策略逻辑函数"""
    try:
        # 判断是否应该执行策略逻辑（回测适配）
        if not should_execute_strategy(ContextInfo):
            return

        # 获取当前时间（回测适配）
        current_time = get_current_time_str(ContextInfo)

        if is_backtest_mode(ContextInfo):
            print(f"[激进版-回测模式] 执行策略，K线时间：{current_time}")
        else:
            print(f"[激进版-实盘模式] 当前为最后一根K线，执行策略，时间：{current_time}")
        
        # 检查账户是否已经登录
        account_data = get_account_info(ContextInfo)
        if not account_data or len(account_data) <= 0:
            log_message("ERROR", "策略运行", "当前账户未登录，请先登录！", None, ContextInfo)
            return

        # 执行激进版核心逻辑
        execute_aggressive_strategy(ContextInfo)

        # 处理交易任务队列
        try:
            task_queue_process_pending_tasks(ContextInfo)
        except Exception as e:
            log_message("ERROR", "策略运行", f"任务队列处理异常：{str(e)}", None, ContextInfo)

        # 记录运行日志
        log_message("INFO", "策略运行", f"激进版策略执行完成，时间：{current_time}", None, ContextInfo)

    except Exception as e:
        error_msg = f"激进版策略执行失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "策略执行", error_msg, None, ContextInfo)


# ==================== 激进版核心逻辑 ====================

def execute_aggressive_strategy(ContextInfo):
    """
    执行激进版策略核心逻辑

    重要：使用异步机制和防重复下单检查

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        # === 第一步：防重复下单检查 ===
        # 检查当天是否已有交易记录（最重要的防重复机制）
        if check_today_trading_records(ContextInfo):
            log_message("INFO", "激进版策略", "检测到当天已有交易记录，跳过策略执行（防重复下单）", None, ContextInfo)
            return

        # === 第二步：检测当前信号状态 ===
        signal_result = detect_latest_signal(ContextInfo)
        if signal_result is None:
            log_message("WARNING", "激进版策略", "无法检测信号状态，跳过本次执行", None, ContextInfo)
            return

        current_phase = signal_result['current_phase']  # 'active' 或 'sleeping'
        latest_signal = signal_result['latest_signal']

        log_message("INFO", "激进版策略", f"当前阶段：{current_phase}，最新信号：{latest_signal['signal_type'] if latest_signal else 'None'}", None, ContextInfo)

        # === 第三步：检查本期间是否已经交易过 ===
        if has_traded_in_current_period(current_phase, latest_signal):
            log_message("INFO", "激进版策略", f"本{current_phase}期间已经交易过，跳过", None, ContextInfo)
            return

        # === 第四步：检查交易时点控制 ===
        time_allowed, time_reason = is_trade_time_allowed(ContextInfo)
        if not time_allowed:
            log_message("INFO", "激进版策略", f"未到交易时点，跳过策略执行：{time_reason}", None, ContextInfo)
            return

        # === 第五步：根据当前阶段执行交易（使用异步机制） ===
        if is_backtest_mode(ContextInfo):
            # 回测模式：使用同步交易
            log_message("INFO", "激进版策略", "回测模式：使用同步交易", None, ContextInfo)
            if current_phase == 'active':
                execute_simple_active_trade_backtest(ContextInfo, latest_signal)
            elif current_phase == 'sleeping':
                execute_simple_sleeping_trade_backtest(ContextInfo, latest_signal)
        else:
            # 实盘模式：使用异步交易
            log_message("INFO", "激进版策略", "实盘模式：使用异步交易机制", None, ContextInfo)
            if current_phase == 'active':
                execute_async_active_trade(ContextInfo, latest_signal)
            elif current_phase == 'sleeping':
                execute_async_sleeping_trade(ContextInfo, latest_signal)

    except Exception as e:
        error_msg = f"激进版策略执行失败：{str(e)}"
        log_message("ERROR", "激进版策略", error_msg, None, ContextInfo)
        print(f"❌ {error_msg}")


def detect_latest_signal(ContextInfo):
    """
    查询最近的历史信号，判断当前是激活期还是沉睡期
    首次运行时会扫描全历史数据建立信号记录

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 包含当前阶段和最新信号信息
    """
    try:
        # 1. 检查数据库中是否有历史信号记录
        latest_db_signal = get_latest_signal_from_db()

        # 2. 如果没有历史信号记录，扫描全历史数据
        if latest_db_signal is None:
            log_message("INFO", "历史扫描", "数据库中没有信号记录，开始扫描全历史数据...", None, ContextInfo)
            scan_and_record_all_historical_signals(ContextInfo)
            # 重新查询最新信号
            latest_db_signal = get_latest_signal_from_db()

        # 3. 根据最近历史信号判断当前阶段
        if latest_db_signal and latest_db_signal['signal_type'] == 'ENTERLONG':
            current_phase = 'active'
            log_message("INFO", "阶段判断", f"根据最近买入信号判断当前为激活期，信号日期：{latest_db_signal.get('signal_date', 'unknown')}", None, ContextInfo)
        elif latest_db_signal and latest_db_signal['signal_type'] == 'EXITLONG':
            current_phase = 'sleeping'
            log_message("INFO", "阶段判断", f"根据最近卖出信号判断当前为沉睡期，信号日期：{latest_db_signal.get('signal_date', 'unknown')}", None, ContextInfo)
        else:
            # 如果扫描后仍没有信号记录，默认为沉睡期
            current_phase = 'sleeping'
            log_message("INFO", "阶段判断", "扫描历史数据后仍无信号记录，默认为沉睡期", None, ContextInfo)

        # 4. 检测当前是否有新信号（用于记录到数据库，但不影响阶段判断）
        has_new_signal = False
        try:
            technical_data = update_technical_indicators(ContextInfo)
            if technical_data is not None:
                current_signal = check_current_signal(technical_data, ContextInfo)
                if current_signal and current_signal['has_signal']:
                    # 记录新信号到数据库
                    record_signal_to_db(current_signal, ContextInfo)
                    has_new_signal = True
                    log_message("INFO", "信号检测", f"检测到新{current_signal['signal_type']}信号并已记录", None, ContextInfo)
        except Exception as e:
            log_message("WARNING", "信号检测", f"检测当前信号时出错：{str(e)}，但不影响阶段判断", None, ContextInfo)

        return {
            'current_phase': current_phase,
            'latest_signal': latest_db_signal,  # 始终返回历史信号用于阶段判断
            'has_new_signal': has_new_signal
        }

    except Exception as e:
        log_message("ERROR", "信号检测", f"检测最新信号失败：{str(e)}", None, ContextInfo)
        return None


def scan_and_record_all_historical_signals(ContextInfo):
    """
    扫描全历史数据，检测并记录所有买卖信号到数据库
    用于策略初始化时建立完整的信号历史

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        log_message("INFO", "历史扫描", "开始扫描全历史K线数据...", None, ContextInfo)

        # 获取全历史日线数据
        market_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close'],
            stock_code=[SIGNAL_FUND_CODE],
            period='1d',
            count=10000,  # 获取足够多的历史数据
            dividend_type='front',
            fill_data=True
        )

        if market_data is None or SIGNAL_FUND_CODE not in market_data:
            log_message("ERROR", "历史扫描", f"无法获取{SIGNAL_FUND_CODE}的历史数据", None, ContextInfo)
            return

        stock_data = market_data[SIGNAL_FUND_CODE]
        if len(stock_data) < 100:  # 至少需要100个交易日的数据
            log_message("WARNING", "历史扫描", f"历史数据不足，只有{len(stock_data)}条记录", None, ContextInfo)
            return

        # 重采样为季线数据
        quarterly_data = resample_daily_to_period(stock_data, EMA_DETECTION_CYCLE)
        if quarterly_data is None or len(quarterly_data) < 2:
            log_message("ERROR", "历史扫描", "无法重采样季线数据或数据不足", None, ContextInfo)
            return

        # 获取收盘价数据并计算EMA
        if hasattr(quarterly_data['close'], 'values'):
            close_prices = [float(x) for x in quarterly_data['close'].values]
            stock_dates = quarterly_data.index.tolist()
        else:
            close_prices = [float(x) for x in quarterly_data['close']]
            stock_dates = list(quarterly_data['index'])

        # 计算EMA指标
        ema_values = calculate_ema(close_prices, EMA_PERIOD)

        if len(ema_values) < 2:
            log_message("ERROR", "历史扫描", "EMA计算结果不足", None, ContextInfo)
            return

        log_message("INFO", "历史扫描", f"开始检测{len(quarterly_data)}个季度的买卖信号...", None, ContextInfo)

        signal_count = 0

        # 从第二个季度开始检测信号（需要前一期数据）
        for i in range(1, len(quarterly_data)):
            try:
                # 当前期数据
                current_close = float(quarterly_data['close'].iloc[i] if hasattr(quarterly_data['close'], 'iloc') else quarterly_data['close'][i])
                current_high = float(quarterly_data['high'].iloc[i] if hasattr(quarterly_data['high'], 'iloc') else quarterly_data['high'][i])
                current_ema = ema_values[i]
                current_bottom_line = current_ema * BOTTOM_RATIO
                current_top_line = current_ema * TOP_RATIO
                current_date = stock_dates[i]

                # 前一期数据
                previous_close = float(quarterly_data['close'].iloc[i-1] if hasattr(quarterly_data['close'], 'iloc') else quarterly_data['close'][i-1])
                previous_high = float(quarterly_data['high'].iloc[i-1] if hasattr(quarterly_data['high'], 'iloc') else quarterly_data['high'][i-1])
                previous_ema = ema_values[i-1]
                previous_bottom_line = previous_ema * BOTTOM_RATIO
                previous_top_line = previous_ema * TOP_RATIO

                # 检测买入信号：收盘价向下穿越底部线
                has_buy_signal = (previous_close >= previous_bottom_line and current_close < current_bottom_line)

                # 买入信号时间限制：只在季度末月份最后7天内有效
                if has_buy_signal:
                    current_month = current_date.month
                    current_day = current_date.day
                    if (current_month in [3, 12] and current_day >= 25) or (current_month in [6, 9] and current_day >= 24):
                        # 记录买入信号
                        signal_data = {
                            'signal_type': 'ENTERLONG',
                            'signal_date': current_date.strftime('%Y-%m-%d'),
                            'signal_price': current_close,
                            'ema_value': current_ema,
                            'bottom_line': current_bottom_line,
                            'top_line': None
                        }
                        record_historical_signal_to_db(signal_data)
                        signal_count += 1
                        log_message("INFO", "历史扫描", f"发现买入信号：{current_date.strftime('%Y-%m-%d')}，价格：{current_close:.4f}", None, ContextInfo)

                # 检测卖出信号：最高价向上穿越顶部线（随时都可以）
                has_sell_signal = (previous_high <= previous_top_line and current_high > current_top_line)

                if has_sell_signal:
                    # 记录卖出信号
                    signal_data = {
                        'signal_type': 'EXITLONG',
                        'signal_date': current_date.strftime('%Y-%m-%d'),
                        'signal_price': current_high,
                        'ema_value': current_ema,
                        'bottom_line': None,
                        'top_line': current_top_line
                    }
                    record_historical_signal_to_db(signal_data)
                    signal_count += 1
                    log_message("INFO", "历史扫描", f"发现卖出信号：{current_date.strftime('%Y-%m-%d')}，价格：{current_high:.4f}", None, ContextInfo)

            except Exception as e:
                log_message("WARNING", "历史扫描", f"处理第{i}个季度数据时出错：{str(e)}", None, ContextInfo)
                continue

        log_message("INFO", "历史扫描", f"历史扫描完成，共发现{signal_count}个信号", None, ContextInfo)

    except Exception as e:
        log_message("ERROR", "历史扫描", f"扫描历史数据失败：{str(e)}", None, ContextInfo)


def record_historical_signal_to_db(signal_data):
    """
    记录历史信号到数据库（使用INSERT OR REPLACE避免重复）

    Args:
        signal_data: 信号数据字典
    """
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 使用INSERT OR REPLACE，以signal_date为唯一键避免重复
        cursor.execute("""
            INSERT OR REPLACE INTO signal_history
            (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
             kline_position, kline_date, is_valid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            signal_data['signal_date'],
            signal_data['signal_type'],
            signal_data['signal_price'],
            signal_data['ema_value'],
            signal_data.get('bottom_line'),
            signal_data.get('top_line'),
            -1,  # 历史扫描时没有kline_position
            signal_data['signal_date'].replace('-', ''),  # 转换为YYYYMMDD格式
            1,  # is_valid
            current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录历史信号到数据库失败：{str(e)}")


# ==================== 交易任务队列类 ====================

class TradeTaskQueue:
    """交易任务队列管理器"""

    def __init__(self):
        self.timeout_thresholds = {
            'WARNING': 60,      # 1分钟：记录警告
            'QUERY': 300,       # 5分钟：主动查询状态
            'ALERT': 1800,      # 30分钟：发送告警
        }

    def create_task_group(self, stock_code: str, target_shares: int, order_reason: str) -> str:
        """
        创建交易任务组

        Args:
            stock_code: 目标股票代码
            target_shares: 目标买入股数
            order_reason: 交易原因

        Returns:
            str: 任务组ID
        """
        task_group_id = str(uuid.uuid4())
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            # 记录任务组创建日志
            self.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"创建交易任务组：目标{stock_code} {target_shares}股，原因：{order_reason}"
            )

            return task_group_id

        except Exception as e:
            error_msg = f"创建任务组失败：{str(e)}"
            print(error_msg)
            self.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
            return task_group_id

    def log_task_message(self, task_group_id: Optional[str] = None, task_id: Optional[int] = None,
                        level: str = "INFO", category: str = "GENERAL",
                        message: str = "", extra_data: Optional[Dict] = None):
        """
        记录任务日志

        Args:
            task_group_id: 任务组ID
            task_id: 任务ID
            level: 日志级别
            category: 日志分类
            message: 日志消息
            extra_data: 额外数据
        """
        try:
            if g_db_connection is None:
                print(f"[{level}] {category}: {message}")
                return

            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            extra_data_json = json.dumps(extra_data, ensure_ascii=False) if extra_data else None

            cursor.execute("""
                INSERT INTO trade_task_log
                (task_id, task_group_id, log_level, log_category, log_message, extra_data, log_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (task_id, task_group_id, level, category, message, extra_data_json, current_time))

            g_db_connection.commit()

            # 同时记录到主日志
            log_message(level, f"任务队列-{category}", message, extra_data, None)

        except Exception as e:
            print(f"记录任务日志失败：{str(e)}")

    def create_account_snapshot(self, task_group_id: str, snapshot_point: str, ContextInfo):
        """
        创建账户快照

        Args:
            task_group_id: 任务组ID
            snapshot_point: 快照时点
            ContextInfo: iQuant上下文信息
        """
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 获取账户信息
            account_info = get_account_info(ContextInfo)
            if not account_info:
                return

            # 获取持仓信息
            sleeping_holdings = get_holdings_from_db(SLEEPING_FUND_CODE)
            active_holdings = get_holdings_from_db(ACTIVE_FUND_CODE)

            # 获取融资信息
            margin_available = account_info.get('margin_available', 0)

            cursor = g_db_connection.cursor()
            cursor.execute("""
                INSERT INTO account_snapshot
                (task_group_id, snapshot_point, available_cash, margin_available,
                 stock_510720_shares, stock_510720_value, stock_159915_shares, stock_159915_value,
                 snapshot_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_group_id,
                snapshot_point,
                account_info.get('available_cash', 0),
                margin_available,
                sleeping_holdings['shares'] if sleeping_holdings else 0,
                sleeping_holdings['total_cost'] if sleeping_holdings else 0,
                active_holdings['shares'] if active_holdings else 0,
                active_holdings['total_cost'] if active_holdings else 0,
                current_time
            ))

            g_db_connection.commit()

        except Exception as e:
            print(f"创建账户快照失败：{str(e)}")

    def calculate_fees(self, amount: float, is_sell: bool = False) -> float:
        """
        计算交易费用

        Args:
            amount: 交易金额
            is_sell: 是否为卖出

        Returns:
            float: 总费用
        """
        # 佣金（买卖都收取）
        commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)

        # 印花税（仅卖出时收取）
        stamp_tax = amount * SELL_TAX_RATE if is_sell else 0

        return commission + stamp_tax

    def create_task(self, task_group_id: str, task_type: str, stock_code: str,
                   target_shares: int, target_amount: Optional[float] = None,
                   depends_on_task: Optional[str] = None, task_params: Optional[Dict] = None, ContextInfo=None) -> tuple:
        """
        创建单个交易任务

        Args:
            task_group_id: 任务组ID
            task_type: 任务类型
            stock_code: 股票代码
            target_shares: 目标股数
            target_amount: 目标金额
            depends_on_task: 依赖的任务ID
            task_params: 任务参数
            ContextInfo: iQuant上下文信息对象

        Returns:
            tuple: (task_id, order_uuid)
        """
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 生成订单UUID用于回调匹配
            order_uuid = str(uuid.uuid4())

            # 估算价格和费用
            estimated_price = get_current_price(ContextInfo, stock_code)
            if target_amount is None:
                target_amount = target_shares * estimated_price if estimated_price else 0

            estimated_fees = self.calculate_fees(target_amount, 'SELL' in task_type)

            cursor = g_db_connection.cursor()
            cursor.execute("""
                INSERT INTO trade_task_queue
                (task_group_id, task_type, stock_code, target_shares, target_amount,
                 estimated_price, estimated_fees, task_status, depends_on_task,
                 order_uuid, task_params, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_group_id,
                task_type,
                stock_code,
                target_shares,
                target_amount,
                estimated_price,
                estimated_fees,
                TaskStatus.PENDING.value,
                depends_on_task,
                order_uuid,
                json.dumps(task_params, ensure_ascii=False) if task_params else None,
                current_time
            ))

            task_id = cursor.lastrowid
            g_db_connection.commit()

            self.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"创建任务：{task_type} {stock_code} {target_shares}股",
                extra_data={'order_uuid': order_uuid}
            )

            return task_id, order_uuid

        except Exception as e:
            error_msg = f"创建任务失败：{str(e)}"
            print(error_msg)
            self.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
            return None, None


def init_task_queue():
    """初始化任务队列"""
    global g_trade_task_queue, g_trade_task_executor, g_trade_task_callback_handler

    try:
        g_trade_task_queue = TradeTaskQueue()
        # 这里可以添加其他任务队列组件的初始化
        print("任务队列组件初始化成功")

    except Exception as e:
        error_msg = f"任务队列初始化失败：{str(e)}"
        print(error_msg)
        raise e


def task_queue_process_pending_tasks(ContextInfo):
    """
    处理待执行任务
    需要在handlebar中调用此函数
    """
    try:
        # 检查ContextInfo是否有效
        if ContextInfo is None:
            print("警告：ContextInfo为None，跳过任务队列处理")
            return

        # 这里可以添加任务执行逻辑
        # 由于我们简化了实现，暂时只记录日志
        log_message("INFO", "任务队列", "任务队列处理完成", None, ContextInfo)

    except Exception as e:
        print(f"处理任务队列失败：{str(e)}")
        log_message("ERROR", "任务队列", f"处理任务队列失败：{str(e)}", None, ContextInfo)


# ==================== 防重复下单检查函数 ====================

def check_today_trading_records(ContextInfo=None) -> bool:
    """
    检查当天是否已有交易记录
    防止同一天内重复交易

    检查范围：
    1. trade_orders 表中的当天交易记录
    2. trade_task_queue 表中的当天任务记录
    3. trade_execution_log 表中的当天执行记录

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 当天是否已有交易
    """
    try:
        if g_db_connection is None:
            log_message("WARNING", "当天交易检查", "数据库连接不可用，跳过检查", None, ContextInfo)
            return False

        cursor = g_db_connection.cursor()

        # 获取当天日期
        if ContextInfo and is_backtest_mode(ContextInfo):
            # 回测模式：使用当前K线时间
            current_time = get_current_time(ContextInfo)
            today = current_time.strftime("%Y-%m-%d")
            log_message("DEBUG", "当天交易检查", f"回测模式，使用K线日期：{today}", None, ContextInfo)
        else:
            # 实盘模式：使用系统时间
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            log_message("DEBUG", "当天交易检查", f"实盘模式，使用系统日期：{today}", None, ContextInfo)

        # === 检查1：trade_orders 表中的当天交易记录 ===
        cursor.execute("""
            SELECT COUNT(*) FROM trade_orders
            WHERE DATE(order_date) = ?
            AND order_status IN ('SUCCESS', 'PENDING')
            AND order_reason IN ('AGGRESSIVE_BUY', 'AGGRESSIVE_SELL', 'SIGNAL_BUY', 'SIGNAL_SELL')
        """, (today,))

        result = cursor.fetchone()
        today_orders = result[0] if result else 0

        if today_orders > 0:
            log_message("INFO", "当天交易检查",
                       f"发现当天已有{today_orders}笔交易订单，防止重复交易", None, ContextInfo)
            return True

        # === 检查2：trade_task_queue 表中的当天任务记录 ===
        cursor.execute("""
            SELECT COUNT(*) FROM trade_task_queue
            WHERE DATE(created_time) = ?
            AND task_status IN ('PENDING', 'EXECUTING', 'WAITING_CALLBACK', 'COMPLETED')
        """, (today,))

        result = cursor.fetchone()
        today_tasks = result[0] if result else 0

        if today_tasks > 0:
            log_message("INFO", "当天交易检查",
                       f"发现当天已有{today_tasks}个交易任务，防止重复交易", None, ContextInfo)
            return True

        # === 检查3：trade_execution_log 表中的当天执行记录 ===
        cursor.execute("""
            SELECT COUNT(*) FROM trade_execution_log
            WHERE DATE(trade_time) = ?
            AND status = 'SUCCESS'
        """, (today,))

        result = cursor.fetchone()
        today_executions = result[0] if result else 0

        if today_executions > 0:
            log_message("INFO", "当天交易检查",
                       f"发现当天已有{today_executions}笔成功执行的交易，防止重复交易", None, ContextInfo)
            return True

        # 所有检查都通过，当天没有交易记录
        log_message("DEBUG", "当天交易检查",
                   f"当天无交易记录：订单{today_orders}笔，任务{today_tasks}个，执行{today_executions}笔",
                   None, ContextInfo)
        return False

    except Exception as e:
        log_message("ERROR", "当天交易检查", f"检查当天交易记录失败：{str(e)}", None, ContextInfo)
        # 出错时为安全起见，返回True（防止重复交易）
        return True


def is_trade_time_allowed(ContextInfo=None) -> Tuple[bool, str]:
    """
    检查当前时点是否允许交易
    实盘模式：检查当前时间是否在设置的交易时点之后
    回测模式：忽略时点控制，直接返回允许

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        tuple: (是否允许交易, 说明信息)
    """
    try:
        # 回测模式忽略时点控制
        if ContextInfo and is_backtest_mode(ContextInfo):
            return (True, "回测模式，忽略时点控制")

        # 检查是否启用时点控制
        if not ENABLE_TIME_CONTROL:
            return (True, "时点控制已禁用")

        # 获取当前时间
        current_time = datetime.datetime.now()
        current_time_str = current_time.strftime("%H%M%S")

        # 解析设置的交易时点
        if len(TRADE_TIME_CONTROL) != 6:
            log_message("ERROR", "时点控制", f"交易时点格式错误：{TRADE_TIME_CONTROL}，应为HHmmss格式", None, ContextInfo)
            return (True, "时点格式错误，允许交易")

        trade_hour = int(TRADE_TIME_CONTROL[:2])
        trade_minute = int(TRADE_TIME_CONTROL[2:4])
        trade_second = int(TRADE_TIME_CONTROL[4:6])

        # 构造今天的交易时点
        trade_time = current_time.replace(hour=trade_hour, minute=trade_minute, second=trade_second, microsecond=0)

        # 检查当前时间是否在交易时点之后
        if current_time >= trade_time:
            return (True, f"当前时间{current_time.strftime('%H:%M:%S')}已过交易时点{TRADE_TIME_CONTROL[:2]}:{TRADE_TIME_CONTROL[2:4]}:{TRADE_TIME_CONTROL[4:6]}")
        else:
            return (False, f"当前时间{current_time.strftime('%H:%M:%S')}未到交易时点{TRADE_TIME_CONTROL[:2]}:{TRADE_TIME_CONTROL[2:4]}:{TRADE_TIME_CONTROL[4:6]}")

    except Exception as e:
        log_message("ERROR", "时点控制", f"检查交易时点失败：{str(e)}", None, ContextInfo)
        return (True, f"时点检查异常，允许交易：{str(e)}")


def has_traded_in_current_period(current_phase: str, latest_signal: Dict) -> bool:
    """
    检查本期间是否已经交易过
    激进版策略：每个阶段只交易一次

    Args:
        current_phase: 当前阶段 ('active' 或 'sleeping')
        latest_signal: 最新信号信息

    Returns:
        bool: 本期间是否已经交易过
    """
    try:
        if g_db_connection is None:
            return False

        cursor = g_db_connection.cursor()

        # 获取最新信号的日期
        if latest_signal and latest_signal.get('signal_date'):
            signal_date = latest_signal['signal_date']
        else:
            # 如果没有信号，使用当天日期
            signal_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # 检查从信号日期开始是否有相关交易
        if current_phase == 'active':
            # 激活期：检查是否有买入激活期基金的交易
            cursor.execute("""
                SELECT COUNT(*) FROM trade_orders
                WHERE order_date >= ?
                AND stock_code = ?
                AND order_type = 'BUY'
                AND order_reason IN ('AGGRESSIVE_BUY', 'SIGNAL_BUY')
                AND order_status = 'SUCCESS'
            """, (signal_date, ACTIVE_FUND_CODE))
        else:  # sleeping
            # 沉睡期：检查是否有卖出激活期基金的交易
            cursor.execute("""
                SELECT COUNT(*) FROM trade_orders
                WHERE order_date >= ?
                AND stock_code = ?
                AND order_type = 'SELL'
                AND order_reason IN ('AGGRESSIVE_SELL', 'SIGNAL_SELL')
                AND order_status = 'SUCCESS'
            """, (signal_date, ACTIVE_FUND_CODE))

        result = cursor.fetchone()
        trade_count = result[0] if result else 0

        has_traded = trade_count > 0

        log_message("DEBUG", "期间交易检查",
                   f"阶段：{current_phase}，信号日期：{signal_date}，"
                   f"相关交易数量：{trade_count}，已交易：{has_traded}", None, None)

        return has_traded

    except Exception as e:
        log_message("ERROR", "期间交易检查", f"检查本期间交易失败：{str(e)}", None, None)
        return False  # 出错时允许交易


# ==================== 异步交易函数 ====================

def execute_async_active_trade(ContextInfo, latest_signal):
    """
    异步执行激活期交易：使用任务队列和回调机制

    Args:
        ContextInfo: iQuant上下文信息对象
        latest_signal: 最新信号信息
    """
    try:
        log_message("INFO", "异步激活期交易", f"开始执行异步激活期交易，目标金额：{AGGRESSIVE_INVESTMENT_AMOUNT:,}元", None, ContextInfo)

        # 创建任务组
        task_group_id = g_trade_task_queue.create_task_group(
            ACTIVE_FUND_CODE,
            0,  # 股数稍后计算
            "AGGRESSIVE_ACTIVE"
        )

        # 1. 先卖出沉睡期基金（如果有）
        sleeping_holdings = get_holdings_from_db(SLEEPING_FUND_CODE)
        if sleeping_holdings and sleeping_holdings['shares'] > 0:
            sell_task_id, sell_uuid = g_trade_task_queue.create_task(
                task_group_id=task_group_id,
                task_type=TaskType.SELL_SLEEPING_FUND.value,
                stock_code=SLEEPING_FUND_CODE,
                target_shares=sleeping_holdings['shares'],
                task_params={'signal_info': latest_signal},
                ContextInfo=ContextInfo
            )

            if sell_task_id:
                # 执行异步卖出
                execute_async_sell_order(ContextInfo, SLEEPING_FUND_CODE, sleeping_holdings['shares'],
                                       "激活期卖出沉睡期基金", sell_uuid)

        # 2. 买入激活期基金
        current_price = get_current_price(ContextInfo, ACTIVE_FUND_CODE)
        if current_price is None:
            log_message("ERROR", "异步激活期交易", f"无法获取{ACTIVE_FUND_CODE}当前价格", None, ContextInfo)
            return

        target_shares = math.floor(AGGRESSIVE_INVESTMENT_AMOUNT / current_price / 100) * 100
        if target_shares < MIN_TRADE_SHARES:
            log_message("WARNING", "异步激活期交易", f"计算的股数{target_shares}小于最小交易股数{MIN_TRADE_SHARES}", None, ContextInfo)
            return

        # 获取账户信息
        account_info = get_account_info(ContextInfo)
        if account_info is None:
            log_message("ERROR", "异步激活期交易", "无法获取账户信息", None, ContextInfo)
            return

        available_cash = account_info.get('available_cash', 0)
        margin_available = account_info.get('margin_available', 0)
        actual_amount = target_shares * current_price

        # 创建买入任务
        if available_cash >= actual_amount:
            # 现金充足，直接买入
            buy_task_id, buy_uuid = g_trade_task_queue.create_task(
                task_group_id=task_group_id,
                task_type=TaskType.BUY_ACTIVE_FUND_CASH.value,
                stock_code=ACTIVE_FUND_CODE,
                target_shares=target_shares,
                target_amount=actual_amount,
                task_params={'signal_info': latest_signal, 'use_margin': False},
                ContextInfo=ContextInfo
            )

            if buy_task_id:
                execute_async_buy_order(ContextInfo, ACTIVE_FUND_CODE, target_shares,
                                      "激活期现金买入", buy_uuid, use_margin=False)
        elif available_cash + margin_available >= actual_amount:
            # 需要融资
            cash_shares = math.floor(available_cash / current_price / 100) * 100
            margin_shares = target_shares - cash_shares

            # 先用现金买入
            if cash_shares >= MIN_TRADE_SHARES:
                cash_task_id, cash_uuid = g_trade_task_queue.create_task(
                    task_group_id=task_group_id,
                    task_type=TaskType.BUY_ACTIVE_FUND_CASH.value,
                    stock_code=ACTIVE_FUND_CODE,
                    target_shares=cash_shares,
                    target_amount=cash_shares * current_price,
                    task_params={'signal_info': latest_signal, 'use_margin': False},
                    ContextInfo=ContextInfo
                )

                if cash_task_id:
                    execute_async_buy_order(ContextInfo, ACTIVE_FUND_CODE, cash_shares,
                                          "激活期现金买入", cash_uuid, use_margin=False)

            # 再用融资买入
            if margin_shares >= MIN_TRADE_SHARES:
                margin_task_id, margin_uuid = g_trade_task_queue.create_task(
                    task_group_id=task_group_id,
                    task_type=TaskType.BUY_ACTIVE_FUND_MARGIN.value,
                    stock_code=ACTIVE_FUND_CODE,
                    target_shares=margin_shares,
                    target_amount=margin_shares * current_price,
                    task_params={'signal_info': latest_signal, 'use_margin': True},
                    ContextInfo=ContextInfo
                )

                if margin_task_id:
                    execute_async_buy_order(ContextInfo, ACTIVE_FUND_CODE, margin_shares,
                                          "激活期融资买入", margin_uuid, use_margin=True)
        else:
            log_message("ERROR", "异步激活期交易", f"资金不足，需要{actual_amount:,.2f}元，可用现金{available_cash:,.2f}元，融资额度{margin_available:,.2f}元", None, ContextInfo)
            return

        log_message("INFO", "异步激活期交易", f"异步激活期交易任务已创建，任务组：{task_group_id}", None, ContextInfo)

    except Exception as e:
        error_msg = f"异步激活期交易失败：{str(e)}"
        log_message("ERROR", "异步激活期交易", error_msg, None, ContextInfo)


def execute_async_sleeping_trade(ContextInfo, latest_signal):
    """
    异步执行沉睡期交易：使用任务队列和回调机制

    Args:
        ContextInfo: iQuant上下文信息对象
        latest_signal: 最新信号信息
    """
    try:
        log_message("INFO", "异步沉睡期交易", "开始执行异步沉睡期交易", None, ContextInfo)

        # 检查激活期基金持仓
        active_holdings = get_holdings_from_db(ACTIVE_FUND_CODE)
        if not active_holdings or active_holdings['shares'] <= 0:
            log_message("INFO", "异步沉睡期交易", f"数据库中没有{ACTIVE_FUND_CODE}持仓记录，无需交易", None, ContextInfo)
            return

        # 创建任务组
        task_group_id = g_trade_task_queue.create_task_group(
            SLEEPING_FUND_CODE,
            0,  # 股数稍后计算
            "AGGRESSIVE_SLEEPING"
        )

        sell_shares = active_holdings['shares']

        # 1. 卖出激活期基金
        sell_task_id, sell_uuid = g_trade_task_queue.create_task(
            task_group_id=task_group_id,
            task_type=TaskType.SELL_ACTIVE_FUND.value,
            stock_code=ACTIVE_FUND_CODE,
            target_shares=sell_shares,
            task_params={'signal_info': latest_signal},
            ContextInfo=ContextInfo
        )

        if sell_task_id:
            execute_async_sell_order(ContextInfo, ACTIVE_FUND_CODE, sell_shares,
                                   "沉睡期卖出激活期基金", sell_uuid)

        # 2. 买入沉睡期基金（依赖卖出任务完成）
        # 估算卖出金额
        active_price = get_current_price(ContextInfo, ACTIVE_FUND_CODE)
        sleeping_price = get_current_price(ContextInfo, SLEEPING_FUND_CODE)

        if active_price and sleeping_price:
            estimated_sell_amount = sell_shares * active_price * 0.999  # 扣除手续费估算
            buy_shares = math.floor(estimated_sell_amount / sleeping_price / 100) * 100

            if buy_shares >= MIN_TRADE_SHARES:
                buy_task_id, buy_uuid = g_trade_task_queue.create_task(
                    task_group_id=task_group_id,
                    task_type=TaskType.BUY_SLEEPING_FUND.value,
                    stock_code=SLEEPING_FUND_CODE,
                    target_shares=buy_shares,
                    target_amount=buy_shares * sleeping_price,
                    depends_on_task=str(sell_task_id),  # 依赖卖出任务
                    task_params={'signal_info': latest_signal, 'use_margin': False},
                    ContextInfo=ContextInfo
                )

                if buy_task_id:
                    execute_async_buy_order(ContextInfo, SLEEPING_FUND_CODE, buy_shares,
                                          "沉睡期买入沉睡期基金", buy_uuid, use_margin=False)

        log_message("INFO", "异步沉睡期交易", f"异步沉睡期交易任务已创建，任务组：{task_group_id}", None, ContextInfo)

    except Exception as e:
        error_msg = f"异步沉睡期交易失败：{str(e)}"
        log_message("ERROR", "异步沉睡期交易", error_msg, None, ContextInfo)


def get_current_price(ContextInfo, stock_code: str) -> float:
    """
    获取股票当前价格（回测适配）

    Args:
        ContextInfo: iQuant上下文信息对象
        stock_code: 股票代码

    Returns:
        float: 当前价格
    """
    try:
        if ContextInfo is None:
            return 0.0

        if is_backtest_mode(ContextInfo):
            # 回测模式：获取当前K线的收盘价
            market_data = ContextInfo.get_market_data_ex(
                fields=['close'],
                stock_code=[stock_code],
                period='1d',
                count=1,
                end_time=g_current_bar_time.strftime('%Y%m%d') if g_current_bar_time else None,
                dividend_type='front',
                fill_data=True
            )

            if market_data and stock_code in market_data:
                close_data = market_data[stock_code]['close']
                if hasattr(close_data, 'iloc'):
                    return float(close_data.iloc[-1])
                else:
                    return float(list(close_data)[-1])
        else:
            # 实盘模式：获取最新价格
            market_data = ContextInfo.get_market_data_ex(
                fields=['close'],
                stock_code=[stock_code],
                period='1min',
                count=1,
                dividend_type='front',
                fill_data=True
            )

            if market_data and stock_code in market_data:
                close_data = market_data[stock_code]['close']
                if hasattr(close_data, 'iloc'):
                    return float(close_data.iloc[-1])
                else:
                    return float(list(close_data)[-1])

        return 0.0

    except Exception as e:
        log_message("ERROR", "价格获取", f"获取{stock_code}价格失败：{str(e)}", None, ContextInfo)
        return 0.0


def get_account_info(ContextInfo) -> Dict:
    """
    获取账户信息

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 账户信息
    """
    try:
        account_data = get_trade_detail_data(ACCOUNT_ID, ACCOUNT_TYPE, 'ACCOUNT')

        if account_data and len(account_data) > 0:
            account = account_data[0]
            return {
                'available_cash': getattr(account, 'm_dAvailable', 0),
                'total_asset': getattr(account, 'm_dBalance', 0),
                'margin_available': getattr(account, 'm_dCredit', 0),
                'market_value': getattr(account, 'm_dMarketValue', 0)
            }

        return {}

    except Exception as e:
        log_message("ERROR", "账户信息", f"获取账户信息失败：{str(e)}", None, ContextInfo)
        return {}


def get_holdings_from_db(stock_code: str) -> Optional[Dict]:
    """
    从数据库获取持仓信息

    Args:
        stock_code: 股票代码

    Returns:
        dict: 持仓信息
    """
    try:
        if g_db_connection is None:
            return None

        cursor = g_db_connection.cursor()
        cursor.execute("""
            SELECT shares, avg_cost, market_value, current_price
            FROM position_records
            WHERE stock_code = ?
            ORDER BY record_date DESC
            LIMIT 1
        """, (stock_code,))

        result = cursor.fetchone()
        if result:
            return {
                'shares': result[0],
                'avg_cost': result[1],
                'market_value': result[2],
                'current_price': result[3],
                'total_cost': result[0] * result[1]  # 计算总成本
            }

        return None

    except Exception as e:
        print(f"从数据库获取持仓失败：{str(e)}")
        return None


def update_holdings_in_db(stock_code: str, trade_type: str, shares: int, price: float):
    """
    更新数据库中的持仓记录

    Args:
        stock_code: 股票代码
        trade_type: 交易类型 ('BUY' 或 'SELL')
        shares: 交易股数
        price: 交易价格
    """
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取当前持仓
        current_holdings = get_holdings_from_db(stock_code)

        if trade_type == 'BUY':
            if current_holdings:
                # 计算新的平均成本
                old_shares = current_holdings['shares']
                old_avg_cost = current_holdings['avg_cost']
                new_shares = old_shares + shares
                new_avg_cost = (old_shares * old_avg_cost + shares * price) / new_shares
            else:
                new_shares = shares
                new_avg_cost = price
        else:  # SELL
            if current_holdings:
                new_shares = max(0, current_holdings['shares'] - shares)
                new_avg_cost = current_holdings['avg_cost']  # 卖出不改变平均成本
            else:
                new_shares = 0
                new_avg_cost = 0

        new_market_value = new_shares * price

        # 更新持仓记录
        cursor.execute("""
            INSERT INTO position_records
            (record_date, stock_code, shares, avg_cost, market_value, current_price, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (current_time, stock_code, new_shares, new_avg_cost, new_market_value, price, current_time))

        g_db_connection.commit()

    except Exception as e:
        print(f"更新持仓记录失败：{str(e)}")


def record_trade_to_db(trade_type: str, stock_code: str, shares: int, amount: float, signal_info: Dict, ContextInfo):
    """
    记录交易到数据库

    Args:
        trade_type: 交易类型
        stock_code: 股票代码
        shares: 股数
        amount: 金额
        signal_info: 信号信息
        ContextInfo: iQuant上下文信息对象
    """
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = get_current_time_str(ContextInfo) if ContextInfo else datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        order_reason = f"AGGRESSIVE_{trade_type}"

        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             actual_shares, actual_price, order_status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'SUCCESS', ?)
        """, (current_time, stock_code, trade_type, order_reason, shares,
              shares, amount/shares if shares > 0 else 0, current_time))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录交易到数据库失败：{str(e)}")


def execute_async_buy_order(ContextInfo, stock_code: str, shares: int, reason: str, order_uuid: str, use_margin: bool = False):
    """
    执行异步买入订单

    Args:
        ContextInfo: iQuant上下文信息对象
        stock_code: 股票代码
        shares: 买入股数
        reason: 交易原因
        order_uuid: 订单UUID（用于回调匹配）
        use_margin: 是否使用融资
    """
    try:
        log_message("INFO", "异步买入", f"执行异步买入：{stock_code} {shares}股，UUID：{order_uuid}，融资：{use_margin}", None, ContextInfo)

        # 使用 passorder 进行异步下单，结果通过回调处理
        if use_margin:
            # 融资买入
            passorder(
                27,  # 融资买入
                1101,  # 可能需要调整为融资买入的订单类型
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '激进版策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo
            )
        else:
            # 现金买入
            passorder(
                33 if ACCOUNT_TYPE == "CREDIT" else 23,  # 买入
                1101,
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '激进版策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo
            )
        log_message("INFO", "异步买入", f"异步买入订单已提交：{stock_code} {shares}股，等待回调", None, ContextInfo)
    except Exception as e:
        error_msg = f"执行异步买入订单失败：{str(e)}"
        log_message("ERROR", "异步买入", error_msg, None, ContextInfo)


def execute_async_sell_order(ContextInfo, stock_code: str, shares: int, reason: str, order_uuid: str):
    """
    执行异步卖出订单

    Args:
        ContextInfo: iQuant上下文信息对象
        stock_code: 股票代码
        shares: 卖出股数
        reason: 交易原因
        order_uuid: 订单UUID（用于回调匹配）
    """
    try:
        log_message("INFO", "异步卖出", f"执行异步卖出：{stock_code} {shares}股，UUID：{order_uuid}", None, ContextInfo)

        # 使用 passorder 进行异步下单，结果通过回调处理
        passorder(
            34 if ACCOUNT_TYPE == "CREDIT" else 24,  # 卖出
            1101,
            ACCOUNT_ID,
            stock_code,
            44,  # 市价
            -1,  # 市价
            shares,
            '激进版策略',
            1,
            order_uuid,  # 使用UUID作为备注，用于回调匹配
            ContextInfo
        )

        log_message("INFO", "异步卖出", f"异步卖出订单已提交：{stock_code} {shares}股，等待回调", None, ContextInfo)

    except Exception as e:
        error_msg = f"执行异步卖出订单失败：{str(e)}"
        log_message("ERROR", "异步卖出", error_msg, None, ContextInfo)


def place_buy_order(ContextInfo, stock_code: str, shares: int, reason: str, use_margin: bool = False) -> bool:
    """
    下买入订单（同步方式，用于简化交易）

    Args:
        ContextInfo: iQuant上下文信息对象
        stock_code: 股票代码
        shares: 买入股数
        reason: 交易原因
        use_margin: 是否使用融资

    Returns:
        bool: 是否成功
    """
    try:
        if is_backtest_mode(ContextInfo):
            # 回测模式：直接返回成功
            log_message("INFO", "回测买入", f"[回测模式] 买入：{stock_code} {shares}股，原因：{reason}", None, ContextInfo)
            return True
        else:
            # 实盘模式：实际下单
            order_uuid = str(uuid.uuid4())

            # 记录订单到数据库
            record_trade_order_to_db(stock_code, "BUY", reason, shares, order_uuid, ContextInfo)

            if use_margin:
                # 融资买入
                passorder(
                    opType=23,  # 融资买入
                    orderType=1101,  # 限价单
                    accountid=ACCOUNT_ID,
                    orderCode=stock_code,
                    priceOrder=0,  # 市价
                    shareOrder=shares,
                    strategyName="激进版策略",
                    quickTrade=6,  # 对方最优价格
                    userOrderId=order_uuid
                )
            else:
                # 现金买入
                passorder(
                    opType=1,   # 现金买入
                    orderType=1101,  # 限价单
                    accountid=ACCOUNT_ID,
                    orderCode=stock_code,
                    priceOrder=0,  # 市价
                    shareOrder=shares,
                    strategyName="激进版策略",
                    quickTrade=6,  # 对方最优价格
                    userOrderId=order_uuid
                )

            log_message("INFO", "实盘买入", f"买入订单已提交：{stock_code} {shares}股，UUID：{order_uuid}", None, ContextInfo)
            return True

    except Exception as e:
        error_msg = f"下买入订单失败：{str(e)}"
        log_message("ERROR", "买入订单", error_msg, None, ContextInfo)
        return False


def place_sell_order(ContextInfo, stock_code: str, shares: int, reason: str) -> bool:
    """
    下卖出订单（同步方式，用于简化交易）

    Args:
        ContextInfo: iQuant上下文信息对象
        stock_code: 股票代码
        shares: 卖出股数
        reason: 交易原因

    Returns:
        bool: 是否成功
    """
    try:
        if is_backtest_mode(ContextInfo):
            # 回测模式：直接返回成功
            log_message("INFO", "回测卖出", f"[回测模式] 卖出：{stock_code} {shares}股，原因：{reason}", None, ContextInfo)
            return True
        else:
            # 实盘模式：实际下单
            order_uuid = str(uuid.uuid4())

            # 记录订单到数据库
            record_trade_order_to_db(stock_code, "SELL", reason, shares, order_uuid, ContextInfo)

            # 卖出
            passorder(
                opType=2,   # 卖出
                orderType=1101,  # 限价单
                accountid=ACCOUNT_ID,
                orderCode=stock_code,
                priceOrder=0,  # 市价
                shareOrder=shares,
                strategyName="激进版策略",
                quickTrade=6,  # 对方最优价格
                userOrderId=order_uuid
            )

            log_message("INFO", "实盘卖出", f"卖出订单已提交：{stock_code} {shares}股，UUID：{order_uuid}", None, ContextInfo)
            return True

    except Exception as e:
        error_msg = f"下卖出订单失败：{str(e)}"
        log_message("ERROR", "卖出订单", error_msg, None, ContextInfo)
        return False


def record_trade_order_to_db(stock_code: str, order_type: str, order_reason: str, shares: int, order_uuid: str, ContextInfo):
    """
    记录交易订单到数据库

    Args:
        stock_code: 股票代码
        order_type: 订单类型
        order_reason: 交易原因
        shares: 股数
        order_uuid: 订单UUID
        ContextInfo: iQuant上下文信息对象
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = get_current_time_str(ContextInfo) if ContextInfo else datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, 'PENDING', ?, ?)
        """, (current_time, stock_code, order_type, order_reason, shares, order_uuid, current_time))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "订单记录", f"记录交易指令失败：{str(e)}", None, ContextInfo)


# ==================== 简化交易函数（回退方案） ====================

def execute_simple_active_trade_backtest(ContextInfo, latest_signal):
    """
    简化的激活期交易：先检查并卖出SLEEPING_FUND_CODE，再买入ACTIVE_FUND_CODE
    注意：这是同步交易，不使用任务队列

    Args:
        ContextInfo: iQuant上下文信息对象
        latest_signal: 最新信号信息
    """
    try:
        log_message("INFO", "简化激活期交易", f"开始执行简化激活期交易，目标金额：{AGGRESSIVE_INVESTMENT_AMOUNT:,}元", None, ContextInfo)

        # 1. 检查并卖出SLEEPING_FUND_CODE
        sleeping_holdings = get_holdings_from_db(SLEEPING_FUND_CODE)
        if sleeping_holdings and sleeping_holdings['shares'] > 0:
            sell_shares = sleeping_holdings['shares']
            log_message("INFO", "简化激活期交易", f"检测到{sell_shares}股{SLEEPING_FUND_CODE}，先卖出", None, ContextInfo)

            sell_result = place_sell_order(ContextInfo, SLEEPING_FUND_CODE, sell_shares, "激活期卖出沉睡期基金")
            if sell_result:
                # 更新数据库持仓记录
                update_holdings_in_db(SLEEPING_FUND_CODE, "SELL", sell_shares, 0)
                record_trade_to_db("SELL", SLEEPING_FUND_CODE, sell_shares, 0, latest_signal, ContextInfo)

        # 2. 买入ACTIVE_FUND_CODE
        current_price = get_current_price(ContextInfo, ACTIVE_FUND_CODE)
        if current_price is None:
            log_message("ERROR", "简化激活期交易", f"无法获取{ACTIVE_FUND_CODE}当前价格", None, ContextInfo)
            return

        target_shares = math.floor(AGGRESSIVE_INVESTMENT_AMOUNT / current_price / 100) * 100
        if target_shares < MIN_TRADE_SHARES:
            log_message("WARNING", "简化激活期交易", f"计算的股数{target_shares}小于最小交易股数{MIN_TRADE_SHARES}", None, ContextInfo)
            return

        # 获取账户信息
        account_info = get_account_info(ContextInfo)
        if account_info is None:
            log_message("ERROR", "简化激活期交易", "无法获取账户信息", None, ContextInfo)
            return

        available_cash = account_info.get('available_cash', 0)
        margin_available = account_info.get('margin_available', 0)
        actual_amount = target_shares * current_price

        # 执行买入
        if available_cash >= actual_amount:
            # 现金充足，直接买入
            buy_result = place_buy_order(ContextInfo, ACTIVE_FUND_CODE, target_shares, "激活期现金买入", use_margin=False)
            if buy_result:
                update_holdings_in_db(ACTIVE_FUND_CODE, "BUY", target_shares, current_price)
                record_trade_to_db("BUY", ACTIVE_FUND_CODE, target_shares, actual_amount, latest_signal, ContextInfo)
        elif available_cash + margin_available >= actual_amount:
            # 需要融资
            cash_shares = math.floor(available_cash / current_price / 100) * 100
            margin_shares = target_shares - cash_shares

            # 先用现金买入
            if cash_shares >= MIN_TRADE_SHARES:
                cash_result = place_buy_order(ContextInfo, ACTIVE_FUND_CODE, cash_shares, "激活期现金买入", use_margin=False)
                if cash_result:
                    update_holdings_in_db(ACTIVE_FUND_CODE, "BUY", cash_shares, current_price)
                    record_trade_to_db("BUY", ACTIVE_FUND_CODE, cash_shares, cash_shares * current_price, latest_signal, ContextInfo)

            # 再用融资买入
            if margin_shares >= MIN_TRADE_SHARES:
                margin_result = place_buy_order(ContextInfo, ACTIVE_FUND_CODE, margin_shares, "激活期融资买入", use_margin=True)
                if margin_result:
                    update_holdings_in_db(ACTIVE_FUND_CODE, "BUY", margin_shares, current_price)
                    record_trade_to_db("BUY", ACTIVE_FUND_CODE, margin_shares, margin_shares * current_price, latest_signal, ContextInfo)
        else:
            log_message("ERROR", "简化激活期交易", f"资金不足，需要{actual_amount:,.2f}元，可用现金{available_cash:,.2f}元，融资额度{margin_available:,.2f}元", None, ContextInfo)
            return

        log_message("INFO", "简化激活期交易", f"简化激活期交易完成", None, ContextInfo)

    except Exception as e:
        error_msg = f"简化激活期交易失败：{str(e)}"
        log_message("ERROR", "简化激活期交易", error_msg, None, ContextInfo)


def execute_simple_sleeping_trade_backtest(ContextInfo, latest_signal):
    """
    简化的沉睡期交易：先卖出ACTIVE_FUND_CODE，再买入SLEEPING_FUND_CODE
    注意：这是同步交易，不使用任务队列

    Args:
        ContextInfo: iQuant上下文信息对象
        latest_signal: 最新信号信息
    """
    try:
        log_message("INFO", "简化沉睡期交易", "开始执行简化沉睡期交易", None, ContextInfo)

        # 1. 检查并卖出ACTIVE_FUND_CODE
        active_holdings = get_holdings_from_db(ACTIVE_FUND_CODE)
        if not active_holdings or active_holdings['shares'] <= 0:
            log_message("INFO", "简化沉睡期交易", f"数据库中没有{ACTIVE_FUND_CODE}持仓记录，无需交易", None, ContextInfo)
            return

        sell_shares = active_holdings['shares']

        # 卖出ACTIVE_FUND_CODE
        sell_result = place_sell_order(ContextInfo, ACTIVE_FUND_CODE, sell_shares, "沉睡期卖出激活期基金")
        if not sell_result:
            log_message("ERROR", "简化沉睡期交易", f"卖出{ACTIVE_FUND_CODE}失败", None, ContextInfo)
            return

        # 更新持仓记录
        update_holdings_in_db(ACTIVE_FUND_CODE, "SELL", sell_shares, 0)

        # 2. 估算卖出金额并买入SLEEPING_FUND_CODE
        active_price = get_current_price(ContextInfo, ACTIVE_FUND_CODE)
        if active_price is None:
            log_message("ERROR", "简化沉睡期交易", f"无法获取{ACTIVE_FUND_CODE}当前价格", None, ContextInfo)
            return

        estimated_sell_amount = sell_shares * active_price * 0.999  # 扣除手续费估算

        # 计算买入SLEEPING_FUND_CODE的股数
        sleeping_price = get_current_price(ContextInfo, SLEEPING_FUND_CODE)
        if sleeping_price is None:
            log_message("ERROR", "简化沉睡期交易", f"无法获取{SLEEPING_FUND_CODE}当前价格", None, ContextInfo)
            return

        buy_shares = math.floor(estimated_sell_amount / sleeping_price / 100) * 100
        if buy_shares >= MIN_TRADE_SHARES:
            # 买入SLEEPING_FUND_CODE（仅现金，不融资）
            buy_result = place_buy_order(ContextInfo, SLEEPING_FUND_CODE, buy_shares, "沉睡期买入沉睡期基金", use_margin=False)
            if buy_result:
                update_holdings_in_db(SLEEPING_FUND_CODE, "BUY", buy_shares, sleeping_price)
                record_trade_to_db("BUY", SLEEPING_FUND_CODE, buy_shares, buy_shares * sleeping_price, latest_signal, ContextInfo)
        else:
            log_message("WARNING", "简化沉睡期交易", f"计算的买入股数{buy_shares}小于最小交易股数{MIN_TRADE_SHARES}", None, ContextInfo)

        # 记录卖出交易
        record_trade_to_db("SELL", ACTIVE_FUND_CODE, sell_shares, sell_shares * active_price, latest_signal, ContextInfo)

        log_message("INFO", "简化沉睡期交易", f"简化沉睡期交易完成", None, ContextInfo)

    except Exception as e:
        error_msg = f"简化沉睡期交易失败：{str(e)}"
        log_message("ERROR", "简化沉睡期交易", error_msg, None, ContextInfo)


def execute_sleeping_phase_trade(ContextInfo, latest_signal):
    """
    执行沉睡期交易：卖出全部ACTIVE_FUND_CODE，买入等值SLEEPING_FUND_CODE

    Args:
        ContextInfo: iQuant上下文信息对象
        latest_signal: 最新信号信息
    """
    try:
        log_message("INFO", "沉睡期交易", "开始执行沉睡期交易", None, ContextInfo)

        # 查询数据库中ACTIVE_FUND_CODE的持仓记录
        active_holdings = get_holdings_from_db(ACTIVE_FUND_CODE)
        if not active_holdings or active_holdings['shares'] <= 0:
            log_message("INFO", "沉睡期交易", f"数据库中没有{ACTIVE_FUND_CODE}持仓记录，无需卖出", None, ContextInfo)
            return

        # 卖出全部ACTIVE_FUND_CODE
        sell_shares = active_holdings['shares']
        sell_result = place_sell_order(ContextInfo, ACTIVE_FUND_CODE, sell_shares, "沉睡期卖出")

        if not sell_result:
            log_message("ERROR", "沉睡期交易", f"卖出{ACTIVE_FUND_CODE}失败", None, ContextInfo)
            return

        # 计算卖出金额（估算）
        active_price = get_current_price(ContextInfo, ACTIVE_FUND_CODE)
        if active_price is None:
            log_message("ERROR", "沉睡期交易", f"无法获取{ACTIVE_FUND_CODE}当前价格", None, ContextInfo)
            return

        sell_amount = sell_shares * active_price * 0.999  # 扣除手续费估算

        # 买入等值的SLEEPING_FUND_CODE
        sleeping_price = get_current_price(ContextInfo, SLEEPING_FUND_CODE)
        if sleeping_price is None:
            log_message("ERROR", "沉睡期交易", f"无法获取{SLEEPING_FUND_CODE}当前价格", None, ContextInfo)
            return

        buy_shares = math.floor(sell_amount / sleeping_price / 100) * 100
        if buy_shares >= MIN_TRADE_SHARES:
            place_buy_order(ContextInfo, SLEEPING_FUND_CODE, buy_shares, "沉睡期买入")

        # 记录交易到数据库
        record_trade_to_db("SELL", ACTIVE_FUND_CODE, sell_shares, sell_shares * active_price, latest_signal, ContextInfo)
        record_trade_to_db("BUY", SLEEPING_FUND_CODE, buy_shares, buy_shares * sleeping_price, latest_signal, ContextInfo)

        log_message("INFO", "沉睡期交易", f"沉睡期交易完成，卖出{sell_shares}股{ACTIVE_FUND_CODE}，买入{buy_shares}股{SLEEPING_FUND_CODE}", None, ContextInfo)

    except Exception as e:
        error_msg = f"沉睡期交易失败：{str(e)}"
        log_message("ERROR", "沉睡期交易", error_msg, None, ContextInfo)


# ==================== 技术指标计算函数 ====================

def update_technical_indicators(ContextInfo):
    """
    更新技术指标
    获取SIGNAL_FUND_CODE的季线数据，计算EMA指标

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 包含当前期和前一期的技术指标数据
    """
    try:
        # 获取日线数据
        required_daily_bars = ContextInfo.barpos + 1

        # 确保g_current_bar_time不为None
        if g_current_bar_time is None:
            current_time = get_current_time(ContextInfo)
        else:
            current_time = g_current_bar_time

        market_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close'],
            stock_code=[SIGNAL_FUND_CODE],
            period='1d',
            end_time=current_time.strftime('%Y%m%d'),
            count=required_daily_bars,
            dividend_type='front',
            fill_data=True
        )

        if market_data is None or len(market_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据", None, ContextInfo)
            return None

        # 获取具体股票的数据
        stock_data = market_data.get(SIGNAL_FUND_CODE)
        if stock_data is None or len(stock_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据", None, ContextInfo)
            return None

        # 重采样为季线数据
        quarterly_data = resample_daily_to_period(stock_data, EMA_DETECTION_CYCLE)
        if quarterly_data is None or len(quarterly_data) < 2:
            log_message("WARNING", "技术指标更新", f"无法重采样{SIGNAL_FUND_CODE}的季线数据或数据不足", None, ContextInfo)
            return None

        # 获取收盘价数据
        if hasattr(quarterly_data['close'], 'values'):
            close_prices = [float(x) for x in quarterly_data['close'].values]
            stock_dates = quarterly_data.index.tolist()
        else:
            close_prices = [float(x) for x in quarterly_data['close']]
            stock_dates = list(quarterly_data['index'])

        # 计算EMA指标
        ema_values = calculate_ema(close_prices, EMA_PERIOD)

        if len(ema_values) < 2:
            log_message("WARNING", "技术指标更新", "EMA计算结果不足，需要至少2个数据点", None, ContextInfo)
            return None

        # 构建当前期和前一期数据
        current_idx = -1
        previous_idx = -2

        current_data = {
            'close': close_prices[current_idx],
            'high': quarterly_data['high'].iloc[current_idx] if hasattr(quarterly_data['high'], 'iloc') else quarterly_data['high'][current_idx],
            'low': quarterly_data['low'].iloc[current_idx] if hasattr(quarterly_data['low'], 'iloc') else quarterly_data['low'][current_idx],
            'ema_value': ema_values[current_idx],
            'bottom_line': ema_values[current_idx] * BOTTOM_RATIO,
            'top_line': ema_values[current_idx] * TOP_RATIO,
            'stock_date': stock_dates[current_idx]
        }

        previous_data = {
            'close': close_prices[previous_idx],
            'high': quarterly_data['high'].iloc[previous_idx] if hasattr(quarterly_data['high'], 'iloc') else quarterly_data['high'][previous_idx],
            'low': quarterly_data['low'].iloc[previous_idx] if hasattr(quarterly_data['low'], 'iloc') else quarterly_data['low'][previous_idx],
            'ema_value': ema_values[previous_idx],
            'bottom_line': ema_values[previous_idx] * BOTTOM_RATIO,
            'top_line': ema_values[previous_idx] * TOP_RATIO,
            'stock_date': stock_dates[previous_idx]
        }

        return {
            'current': current_data,
            'previous': previous_data
        }

    except Exception as e:
        error_msg = f"技术指标更新失败：{str(e)}"
        log_message("ERROR", "技术指标更新", error_msg, None, ContextInfo)
        return None


def resample_daily_to_period(daily_data, period_type='1q', use_data_based_grouping=True):
    """
    将日线数据重采样为指定周期的数据，支持基于数据分布的智能分组

    Args:
        daily_data: 日线数据，包含 open, high, low, close 字段
        period_type: 周期类型，'1q'=季线, '1mon'=月线
        use_data_based_grouping: 是否使用基于数据分布的分组方法（推荐）

    Returns:
        重采样后的数据
    """
    try:
        import pandas as pd
    except ImportError:
        # 如果pandas不可用，返回None
        print("[重采样调试] pandas不可用，跳过重采样")
        return None

    # print(f"[重采样调试] 开始重采样，目标周期: {period_type}")

    try:
        # 如果数据为空，返回None
        if daily_data is None:
            print("[重采样调试] 输入数据为None")
            return None

        # print(f"[重采样调试] 输入数据类型: {type(daily_data)}")
        # print(f"[重采样调试] 输入数据形状: {daily_data.shape if hasattr(daily_data, 'shape') else '无shape属性'}")

        # 将数据转换为DataFrame（如果还不是的话）
        if not isinstance(daily_data, pd.DataFrame):
            # print(f"[重采样调试] 数据不是DataFrame，尝试转换...")
            if isinstance(daily_data, dict):
                daily_data = pd.DataFrame(daily_data)
                # print(f"[重采样调试] 从字典转换为DataFrame成功，形状: {daily_data.shape}")
            else:
                # print(f"[重采样调试] 无法转换数据类型，返回原数据")
                return daily_data

        # 确保数据有足够的行数
        if len(daily_data) == 0:
            # print("[重采样调试] 数据为空，返回原数据")
            return daily_data

        # print(f"[重采样调试] 数据列: {list(daily_data.columns)}")
        # print(f"[重采样调试] 当前索引类型: {type(daily_data.index)}")
        # print(f"[重采样调试] 索引前5个值: {daily_data.index[:5].tolist()}")

        # 如果没有时间索引，尝试将现有索引转换为时间索引
        if not isinstance(daily_data.index, pd.DatetimeIndex):
            # print("[重采样调试] 检测到非时间索引，尝试转换为时间索引...")

            try:
                # 尝试将索引转换为日期时间格式
                # 支持多种常见的日期格式
                original_index = daily_data.index.astype(str)
                # print(f"[重采样调试] 原始索引示例: {original_index[:3].tolist()}")

                # 尝试不同的日期格式
                date_formats = ['%Y%m%d', '%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y']
                new_index = None

                for fmt in date_formats:
                    try:
                        new_index = pd.to_datetime(original_index, format=fmt)
                        # print(f"[重采样调试] 成功使用格式 {fmt} 转换日期索引")
                        break
                    except:
                        continue

                # 如果所有格式都失败，尝试自动推断
                if new_index is None:
                    try:
                        new_index = pd.to_datetime(original_index)
                        # print("[重采样调试] 使用自动推断成功转换日期索引")
                    except:
                        # print("[重采样调试] 警告：无法解析日期格式，使用虚拟日期索引")
                        # 最后的备选方案：创建虚拟日期索引
                        end_date = pd.Timestamp.now().normalize()
                        start_date = end_date - pd.Timedelta(days=len(daily_data)-1)
                        new_index = pd.date_range(start=start_date, periods=len(daily_data), freq='D')

                daily_data.index = new_index
                # print(f"[重采样调试] 已创建时间索引: {daily_data.index[0]} 到 {daily_data.index[-1]}")

            except Exception as e:
                print(f"[重采样调试] 时间索引转换失败: {str(e)}")
                return daily_data

        # 根据周期类型设置重采样规则
        if period_type == '1q':
            rule = 'Q'  # 季度末
        elif period_type == '1mon':
            rule = 'M'  # 月末
        else:
            print(f"[重采样调试] 不支持的周期类型: {period_type}，返回原数据")
            return daily_data

        # print(f"[重采样调试] 使用重采样规则: {rule}")

        # 准备重采样的聚合规则
        agg_rules = {}
        for col in daily_data.columns:
            if col in ['open']:
                agg_rules[col] = 'first'
            elif col in ['high']:
                agg_rules[col] = 'max'
            elif col in ['low']:
                agg_rules[col] = 'min'
            elif col in ['close']:
                agg_rules[col] = 'last'
            elif col in ['volume', 'amount']:
                agg_rules[col] = 'sum'
            else:
                agg_rules[col] = 'last'  # 默认取最后一个值

        # print(f"[重采样调试] 聚合规则: {agg_rules}")

        # 选择重采样方法
        if use_data_based_grouping:
            # print("[重采样调试] 使用基于数据分布的智能分组方法...")
            resampled = resample_by_data_distribution(daily_data, period_type, agg_rules)
        else:
            # print("[重采样调试] 使用标准pandas重采样方法...")
            resampled = daily_data.resample(rule).agg(agg_rules).dropna()

        if resampled is None or len(resampled) == 0:
            # print("[重采样调试] 警告：重采样后数据为空！")
            return None

        # print(f"[重采样调试] 重采样完成！")
        # print(f"[重采样调试] 原始数据: {len(daily_data)} 行")
        # print(f"[重采样调试] 重采样后: {len(resampled)} 行")
        # print(f"[重采样调试] 重采样后索引: {resampled.index.tolist()}")

        return resampled

    except Exception as e:
        error_msg = f"重采样失败：{str(e)}"
        print(f"[重采样调试] 错误: {error_msg}")
        log_message("ERROR", "数据重采样", error_msg, None)
        # 重要：不要返回原数据，而是返回None或抛出异常
        raise Exception(f"数据重采样失败: {str(e)}")

def resample_by_data_distribution(daily_data, period_type, agg_rules):
    """
    基于数据分布的智能重采样方法
    通过分析日期间隔来确定周期边界，无需外部交易日历

    Args:
        daily_data: 日线数据
        period_type: 周期类型
        agg_rules: 聚合规则

    Returns:
        重采样后的数据
    """
    import pandas as pd

    try:
        # print("[智能分组] 开始分析数据分布...")

        # 获取所有日期
        dates = daily_data.index
        # print(f"[智能分组] 数据日期范围: {dates[0]} 到 {dates[-1]}")

        # 根据周期类型进行分组
        if period_type == '1q':
            groups = group_by_quarters(dates, daily_data)
        elif period_type == '1mon':
            groups = group_by_months(dates, daily_data)
        else:
            print(f"[智能分组] 不支持的周期类型: {period_type}")
            return None

        if not groups:
            print("[智能分组] 未能创建有效分组")
            return None

        # print(f"[智能分组] 创建了 {len(groups)} 个分组")

        # 对每个分组进行聚合
        result_data = []
        result_index = []

        for group_end_date, group_data in groups:
            # 应用聚合规则
            aggregated_row = {}
            for col, agg_func in agg_rules.items():
                if col in group_data.columns:
                    col_data = group_data[col]
                    if agg_func == 'first':
                        if hasattr(col_data, 'iloc'):
                            aggregated_row[col] = col_data.iloc[0]
                        else:
                            aggregated_row[col] = list(col_data)[0]
                    elif agg_func == 'last':
                        if hasattr(col_data, 'iloc'):
                            aggregated_row[col] = col_data.iloc[-1]
                        else:
                            aggregated_row[col] = list(col_data)[-1]
                    elif agg_func == 'max':
                        aggregated_row[col] = col_data.max()
                    elif agg_func == 'min':
                        aggregated_row[col] = col_data.min()
                    elif agg_func == 'sum':
                        aggregated_row[col] = col_data.sum()
                    else:
                        # 默认取最后一个
                        if hasattr(col_data, 'iloc'):
                            aggregated_row[col] = col_data.iloc[-1]
                        else:
                            aggregated_row[col] = list(col_data)[-1]

            result_data.append(aggregated_row)
            result_index.append(group_end_date)

        # 创建结果DataFrame
        result_df = pd.DataFrame(result_data, index=result_index)

        # print(f"[智能分组] 聚合完成，结果包含 {len(result_df)} 行")
        # print(f"[智能分组] 结果索引: {result_df.index.tolist()}")

        return result_df

    except Exception as e:
        print(f"[智能分组] 分组失败: {str(e)}")
        return None

def group_by_quarters(dates, daily_data):
    """
    按季度分组，基于数据分布确定季度边界
    """
    import pandas as pd

    groups = []
    current_quarter = None
    current_group_data = []
    current_group_start_idx = 0

    # print("[季度分组] 开始按季度分组...")

    for i, date in enumerate(dates):
        quarter = (date.month - 1) // 3 + 1
        year_quarter = (date.year, quarter)

        if current_quarter is None:
            current_quarter = year_quarter
            current_group_start_idx = i

        if year_quarter != current_quarter:
            # 季度变化，保存当前分组
            group_end_date = dates[i-1]  # 上一个日期作为季度末
            if hasattr(daily_data, 'iloc'):
                group_data = daily_data.iloc[current_group_start_idx:i]
            else:
                # 旧版pandas兼容
                group_data = daily_data[current_group_start_idx:i]
            groups.append((group_end_date, group_data))

            # print(f"[季度分组] 季度 {current_quarter[0]}Q{current_quarter[1]} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

            # 开始新的季度
            current_quarter = year_quarter
            current_group_start_idx = i

    # 处理最后一个季度
    if current_group_start_idx < len(dates) and current_quarter is not None:
        group_end_date = dates[-1]
        if hasattr(daily_data, 'iloc'):
            group_data = daily_data.iloc[current_group_start_idx:]
        else:
            # 旧版pandas兼容
            group_data = daily_data[current_group_start_idx:]
        groups.append((group_end_date, group_data))
        # print(f"[季度分组] 季度 {current_quarter[0]}Q{current_quarter[1]} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

    return groups


def group_by_months(dates, daily_data):
    """
    按月份分组，基于数据分布确定月份边界
    """
    import pandas as pd

    groups = []
    current_month = None
    current_group_data = []
    current_group_start_idx = 0

    # print("[月份分组] 开始按月份分组...")

    for i, date in enumerate(dates):
        year_month = (date.year, date.month)

        if current_month is None:
            current_month = year_month
            current_group_start_idx = i

        if year_month != current_month:
            # 月份变化，保存当前分组
            group_end_date = dates[i-1]  # 上一个日期作为月末
            if hasattr(daily_data, 'iloc'):
                group_data = daily_data.iloc[current_group_start_idx:i]
            else:
                # 旧版pandas兼容
                group_data = daily_data[current_group_start_idx:i]
            groups.append((group_end_date, group_data))

            # print(f"[月份分组] 月份 {current_month[0]}-{current_month[1]:02d} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

            # 开始新的月份
            current_month = year_month
            current_group_start_idx = i

    # 处理最后一个月份
    if current_group_start_idx < len(dates) and current_month is not None:
        group_end_date = dates[-1]
        if hasattr(daily_data, 'iloc'):
            group_data = daily_data.iloc[current_group_start_idx:]
        else:
            # 旧版pandas兼容
            group_data = daily_data[current_group_start_idx:]
        groups.append((group_end_date, group_data))
        # print(f"[月份分组] 月份 {current_month[0]}-{current_month[1]:02d} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

    return groups

def calculate_ema(prices: List[float], period: int) -> List[float]:
    """
    计算指数移动平均线(EMA)

    Args:
        prices: 价格序列
        period: EMA周期

    Returns:
        List[float]: EMA值序列
    """
    if len(prices) < 1:
        return []

    ema_values = []
    multiplier = 2.0 / (period + 1)

    # 第一个EMA值使用第一个价格
    ema_values.append(prices[0])

    # 计算后续的EMA值
    for i in range(1, len(prices)):
        ema = round(ema_values[-1] + multiplier * (prices[i] - ema_values[-1]), 3)
        ema_values.append(ema)

    return ema_values


# ==================== 信号检测和数据库函数 ====================

def check_current_signal(technical_data, ContextInfo):
    """
    检查当前是否有新的买卖信号

    Args:
        technical_data: 技术指标数据
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 信号检测结果
    """
    try:
        current_data = technical_data['current']
        previous_data = technical_data['previous']

        current_close = current_data['close']
        current_high = current_data['high']
        current_low = current_data['low']
        bottom_line = current_data['bottom_line']
        top_line = current_data['top_line']

        previous_high = previous_data['high']
        previous_low = previous_data['low']

        # 买入信号：收盘价向下穿越底部线
        has_buy_signal = (previous_data['close'] >= previous_data['bottom_line'] and
                         current_close < bottom_line)

        # 卖出信号：最高价向上穿越顶部线
        has_sell_signal = (previous_data['high'] <= previous_data['top_line'] and
                          current_high > top_line)

        if has_buy_signal:
            return {
                'has_signal': True,
                'signal_type': 'ENTERLONG',
                'signal_price': current_close,
                'ema_value': current_data['ema_value'],
                'bottom_line': bottom_line,
                'top_line': None,
                'signal_date': current_data['stock_date']
            }
        elif has_sell_signal:
            return {
                'has_signal': True,
                'signal_type': 'EXITLONG',
                'signal_price': current_close,
                'ema_value': current_data['ema_value'],
                'bottom_line': None,
                'top_line': top_line,
                'signal_date': current_data['stock_date']
            }
        else:
            return {
                'has_signal': False,
                'signal_type': None
            }

    except Exception as e:
        log_message("ERROR", "信号检测", f"检查当前信号失败：{str(e)}", None, ContextInfo)
        return {'has_signal': False, 'signal_type': None}


def get_latest_signal_from_db():
    """
    从数据库获取最新的信号记录

    Returns:
        dict: 最新信号信息
    """
    try:
        if g_db_connection is None:
            return None

        cursor = g_db_connection.cursor()
        cursor.execute("""
            SELECT signal_type, signal_date, signal_price, ema_value, bottom_line, top_line
            FROM signal_history
            WHERE is_valid = 1
            ORDER BY signal_date DESC, id DESC
            LIMIT 1
        """)

        result = cursor.fetchone()
        if result:
            return {
                'signal_type': result[0],
                'signal_date': result[1],
                'signal_price': result[2],
                'ema_value': result[3],
                'bottom_line': result[4],
                'top_line': result[5]
            }
        else:
            return None

    except Exception as e:
        print(f"获取最新信号失败：{str(e)}")
        return None


def record_signal_to_db(signal_info, ContextInfo):
    """
    记录信号到数据库

    Args:
        signal_info: 信号信息
        ContextInfo: iQuant上下文信息对象
    """
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取K线位置和日期
        current_kline_position = ContextInfo.barpos
        try:
            current_bar_timestamp = ContextInfo.get_bar_timetag(current_kline_position)
            if current_bar_timestamp:
                kline_datetime = datetime.datetime.fromtimestamp(current_bar_timestamp / 1000)
                current_kline_date = kline_datetime.strftime("%Y%m%d")
            else:
                current_kline_date = datetime.datetime.now().strftime("%Y%m%d")
        except:
            current_kline_date = datetime.datetime.now().strftime("%Y%m%d")

        cursor.execute("""
            INSERT INTO signal_history
            (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
             kline_position, kline_date, is_valid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            signal_info['signal_date'],
            signal_info['signal_type'],
            signal_info['signal_price'],
            signal_info['ema_value'],
            signal_info.get('bottom_line'),
            signal_info.get('top_line'),
            current_kline_position,
            current_kline_date,
            1,  # is_valid
            current_time
        ))

        g_db_connection.commit()
        log_message("INFO", "信号记录", f"记录{signal_info['signal_type']}信号到数据库", None, ContextInfo)

    except Exception as e:
        log_message("ERROR", "信号记录", f"记录信号到数据库失败：{str(e)}", None, ContextInfo)


def has_traded_in_current_period(current_phase, latest_signal):
    """
    检查本期间是否已经交易过

    Args:
        current_phase: 当前阶段 ('active' 或 'sleeping')
        latest_signal: 最新信号信息

    Returns:
        bool: 是否已经交易过
    """
    try:
        if g_db_connection is None or latest_signal is None:
            return False

        cursor = g_db_connection.cursor()
        signal_date = latest_signal['signal_date']

        if current_phase == 'active':
            # 检查激活期是否已经买入过
            cursor.execute("""
                SELECT COUNT(*) FROM trade_records
                WHERE trade_type = 'BUY'
                AND stock_code = ?
                AND signal_date = ?
            """, (ACTIVE_FUND_CODE, signal_date))
        else:
            # 检查沉睡期是否已经卖出过
            cursor.execute("""
                SELECT COUNT(*) FROM trade_records
                WHERE trade_type = 'SELL'
                AND stock_code = ?
                AND signal_date = ?
            """, (ACTIVE_FUND_CODE, signal_date))

        result = cursor.fetchone()
        return result[0] > 0 if result else False

    except Exception as e:
        print(f"检查交易记录失败：{str(e)}")
        return False


# ==================== 数据库和日志函数 ====================

def init_database():
    """初始化SQLite数据库"""
    global g_db_connection

    try:
        g_db_connection = sqlite3.connect(DATABASE_PATH)
        cursor = g_db_connection.cursor()

        # 创建信号历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS signal_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_date TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                signal_price REAL NOT NULL,
                ema_value REAL NOT NULL,
                bottom_line REAL,
                top_line REAL,
                kline_position INTEGER,
                kline_date TEXT,
                is_valid INTEGER NOT NULL,
                filter_reason TEXT,
                created_time TEXT NOT NULL
            )
        """)

        # 创建交易记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trade_date TEXT NOT NULL,
                trade_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                shares INTEGER NOT NULL,
                price REAL NOT NULL,
                amount REAL NOT NULL,
                signal_date TEXT,
                signal_type TEXT,
                trade_reason TEXT,
                created_time TEXT NOT NULL
            )
        """)

        # 创建持仓记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS holdings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL UNIQUE,
                shares INTEGER NOT NULL,
                avg_cost REAL NOT NULL,
                total_cost REAL NOT NULL,
                last_update TEXT NOT NULL
            )
        """)

        # 创建交易日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,
                kline_date TEXT,
                log_type TEXT NOT NULL,
                operation TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                is_backtest INTEGER DEFAULT 0,
                created_time TEXT NOT NULL
            )
        """)

        # 创建策略状态表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                current_phase TEXT NOT NULL,
                last_check_time TEXT NOT NULL,
                created_time TEXT NOT NULL,
                updated_time TEXT NOT NULL
            )
        """)

        # 创建交易订单表（用于防重复下单检查）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_date TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                order_type TEXT NOT NULL,
                order_reason TEXT NOT NULL,
                target_shares INTEGER NOT NULL,
                actual_shares INTEGER DEFAULT 0,
                actual_price REAL DEFAULT 0,
                order_status TEXT NOT NULL,
                order_uuid TEXT,
                order_id TEXT,
                error_message TEXT,
                created_time TEXT NOT NULL
            )
        """)

        # 创建交易任务队列表（异步交易核心）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_task_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT NOT NULL,
                task_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                target_shares INTEGER NOT NULL,
                target_amount REAL,
                estimated_price REAL,
                estimated_fees REAL,
                task_status TEXT NOT NULL,
                depends_on_task TEXT,
                order_uuid TEXT UNIQUE,
                order_id TEXT,
                task_params TEXT,
                error_message TEXT,
                created_time TEXT NOT NULL,
                updated_time TEXT
            )
        """)

        # 创建交易执行日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_execution_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trade_time TEXT NOT NULL,
                trade_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                shares INTEGER NOT NULL,
                price REAL NOT NULL,
                amount REAL NOT NULL,
                commission REAL DEFAULT 0,
                stamp_tax REAL DEFAULT 0,
                transfer_fee REAL DEFAULT 0,
                net_amount REAL NOT NULL,
                fees REAL DEFAULT 0,
                order_id TEXT,
                order_uuid TEXT,
                status TEXT NOT NULL,
                created_time TEXT NOT NULL
            )
        """)

        # 创建任务日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_task_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER,
                task_group_id TEXT,
                log_level TEXT NOT NULL,
                log_category TEXT NOT NULL,
                log_message TEXT NOT NULL,
                extra_data TEXT,
                log_time TEXT NOT NULL
            )
        """)

        # 创建订单状态历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_uuid TEXT NOT NULL,
                order_id TEXT,
                stock_code TEXT,
                order_status INTEGER,
                status_desc TEXT,
                volume_traded INTEGER DEFAULT 0,
                volume_total INTEGER DEFAULT 0,
                callback_time TEXT NOT NULL,
                created_time TEXT NOT NULL
            )
        """)

        # 创建持仓记录表（增强版）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS position_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                record_date TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                shares INTEGER NOT NULL,
                avg_cost REAL NOT NULL,
                market_value REAL NOT NULL,
                current_price REAL NOT NULL,
                period_number INTEGER,
                target_value REAL,
                created_time TEXT NOT NULL
            )
        """)

        # 创建账户快照表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_snapshot (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT,
                snapshot_point TEXT NOT NULL,
                available_cash REAL DEFAULT 0,
                margin_available REAL DEFAULT 0,
                stock_510720_shares INTEGER DEFAULT 0,
                stock_510720_value REAL DEFAULT 0,
                stock_159915_shares INTEGER DEFAULT 0,
                stock_159915_value REAL DEFAULT 0,
                snapshot_time TEXT NOT NULL
            )
        """)

        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_date ON signal_history(signal_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_records_date ON trade_records(trade_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_logs_date ON trade_logs(log_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_date ON trade_orders(order_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_uuid ON trade_orders(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_uuid ON trade_task_queue(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_time ON trade_execution_log(trade_time)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_position_records_date ON position_records(record_date)")

        g_db_connection.commit()
        print("✅ 激进版数据库初始化完成（包含异步任务队列表）")
        print("📊 总共创建了 12 个表和相关索引")
        print("🔄 异步机制表：trade_task_queue, trade_orders, trade_execution_log")
        print("🚫 防重复下单表：trade_orders, order_status_history")

        return True

    except Exception as e:
        error_msg = f"数据库初始化失败：{str(e)}"
        print(error_msg)
        return False


def check_table_exists(table_name: str) -> bool:
    """
    检查表是否存在

    Args:
        table_name: 表名

    Returns:
        bool: 表是否存在
    """
    try:
        if g_db_connection is None:
            return False

        cursor = g_db_connection.cursor()
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name=?
        """, (table_name,))
        return cursor.fetchone() is not None

    except Exception as e:
        print(f"检查表存在性失败：{str(e)}")
        return False


def get_database_info() -> Dict:
    """
    获取数据库信息

    Returns:
        dict: 数据库信息
    """
    try:
        if g_db_connection is None:
            return {"error": "数据库连接不可用"}

        cursor = g_db_connection.cursor()

        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]

        # 检查关键表是否存在
        required_tables = [
            'signal_history', 'trade_records', 'holdings', 'trade_logs', 'strategy_status',
            'trade_orders', 'trade_task_queue', 'trade_execution_log', 'order_status_history',
            'position_records', 'account_snapshot', 'trade_task_log'
        ]

        missing_tables = [table for table in required_tables if table not in table_names]

        # 获取各表的记录数
        table_counts = {}
        for table in table_names:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_counts[table] = count
            except:
                table_counts[table] = "N/A"

        return {
            "total_tables": len(table_names),
            "table_names": table_names,
            "required_tables": required_tables,
            "missing_tables": missing_tables,
            "table_counts": table_counts,
            "is_complete": len(missing_tables) == 0
        }

    except Exception as e:
        return {"error": f"获取数据库信息失败：{str(e)}"}


def migrate_database_to_async() -> bool:
    """
    数据库迁移：添加异步机制所需的表
    用于升级现有数据库到支持异步机制的版本

    Returns:
        bool: 是否成功
    """
    try:
        if g_db_connection is None:
            print("❌ 数据库连接不可用")
            return False

        cursor = g_db_connection.cursor()
        print("开始数据库迁移：添加异步机制支持...")

        # 需要添加的表和对应的创建语句
        async_tables = {
            'trade_orders': """
                CREATE TABLE trade_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_date TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    order_type TEXT NOT NULL,
                    order_reason TEXT NOT NULL,
                    target_shares INTEGER NOT NULL,
                    actual_shares INTEGER DEFAULT 0,
                    actual_price REAL DEFAULT 0,
                    order_status TEXT NOT NULL,
                    order_uuid TEXT,
                    order_id TEXT,
                    error_message TEXT,
                    created_time TEXT NOT NULL
                )
            """,
            'trade_task_queue': """
                CREATE TABLE trade_task_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_group_id TEXT NOT NULL,
                    task_type TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    target_shares INTEGER NOT NULL,
                    target_amount REAL,
                    estimated_price REAL,
                    estimated_fees REAL,
                    task_status TEXT NOT NULL,
                    depends_on_task TEXT,
                    order_uuid TEXT UNIQUE,
                    order_id TEXT,
                    task_params TEXT,
                    error_message TEXT,
                    created_time TEXT NOT NULL,
                    updated_time TEXT
                )
            """,
            'trade_execution_log': """
                CREATE TABLE trade_execution_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_time TEXT NOT NULL,
                    trade_type TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    shares INTEGER NOT NULL,
                    price REAL NOT NULL,
                    amount REAL NOT NULL,
                    commission REAL DEFAULT 0,
                    stamp_tax REAL DEFAULT 0,
                    transfer_fee REAL DEFAULT 0,
                    net_amount REAL NOT NULL,
                    fees REAL DEFAULT 0,
                    order_id TEXT,
                    order_uuid TEXT,
                    status TEXT NOT NULL,
                    created_time TEXT NOT NULL
                )
            """,
            'order_status_history': """
                CREATE TABLE order_status_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_uuid TEXT NOT NULL,
                    order_id TEXT,
                    stock_code TEXT,
                    order_status INTEGER,
                    status_desc TEXT,
                    volume_traded INTEGER DEFAULT 0,
                    volume_total INTEGER DEFAULT 0,
                    callback_time TEXT NOT NULL,
                    created_time TEXT NOT NULL
                )
            """,
            'position_records': """
                CREATE TABLE position_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    record_date TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    shares INTEGER NOT NULL,
                    avg_cost REAL NOT NULL,
                    market_value REAL NOT NULL,
                    current_price REAL NOT NULL,
                    period_number INTEGER,
                    target_value REAL,
                    created_time TEXT NOT NULL
                )
            """,
            'account_snapshot': """
                CREATE TABLE account_snapshot (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_group_id TEXT,
                    snapshot_point TEXT NOT NULL,
                    available_cash REAL DEFAULT 0,
                    margin_available REAL DEFAULT 0,
                    stock_510720_shares INTEGER DEFAULT 0,
                    stock_510720_value REAL DEFAULT 0,
                    stock_159915_shares INTEGER DEFAULT 0,
                    stock_159915_value REAL DEFAULT 0,
                    snapshot_time TEXT NOT NULL
                )
            """,
            'trade_task_log': """
                CREATE TABLE trade_task_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER,
                    task_group_id TEXT,
                    log_level TEXT NOT NULL,
                    log_category TEXT NOT NULL,
                    log_message TEXT NOT NULL,
                    extra_data TEXT,
                    log_time TEXT NOT NULL
                )
            """
        }

        # 检查并创建缺失的表
        created_tables = []
        for table_name, create_sql in async_tables.items():
            if not check_table_exists(table_name):
                cursor.execute(create_sql)
                created_tables.append(table_name)
                print(f"✅ 创建表：{table_name}")

        # 创建缺失的索引
        async_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_trade_orders_date ON trade_orders(order_date)",
            "CREATE INDEX IF NOT EXISTS idx_trade_orders_uuid ON trade_orders(order_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_trade_task_queue_uuid ON trade_task_queue(order_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_trade_task_queue_group ON trade_task_queue(task_group_id)",
            "CREATE INDEX IF NOT EXISTS idx_trade_execution_log_time ON trade_execution_log(trade_time)",
            "CREATE INDEX IF NOT EXISTS idx_trade_execution_log_uuid ON trade_execution_log(order_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_position_records_date ON position_records(record_date)",
            "CREATE INDEX IF NOT EXISTS idx_position_records_code ON position_records(stock_code)",
            "CREATE INDEX IF NOT EXISTS idx_order_status_history_uuid ON order_status_history(order_uuid)"
        ]

        for index_sql in async_indexes:
            cursor.execute(index_sql)

        g_db_connection.commit()

        if created_tables:
            print(f"🎉 数据库迁移完成！新增了 {len(created_tables)} 个表：")
            for table in created_tables:
                print(f"   - {table}")
        else:
            print("✅ 数据库已是最新版本，无需迁移")

        print("📊 异步机制支持已启用")
        return True

    except Exception as e:
        print(f"❌ 数据库迁移失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False


def reset_database() -> bool:
    """
    重置数据库：删除所有数据但保留表结构

    Returns:
        bool: 是否成功
    """
    try:
        if g_db_connection is None:
            print("❌ 数据库连接不可用")
            return False

        cursor = g_db_connection.cursor()
        print("开始重置数据库...")

        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        # 清空所有表的数据
        for table in tables:
            table_name = table[0]
            cursor.execute(f"DELETE FROM {table_name}")
            print(f"✅ 清空表：{table_name}")

        g_db_connection.commit()
        print("🎉 数据库重置完成！所有数据已清空，表结构保留")
        return True

    except Exception as e:
        print(f"❌ 数据库重置失败：{str(e)}")
        return False


def print_database_status():
    """
    打印数据库状态信息
    用于调试和监控
    """
    try:
        print("\n" + "=" * 60)
        print("📊 激进版策略数据库状态")
        print("=" * 60)

        db_info = get_database_info()

        if "error" in db_info:
            print(f"❌ {db_info['error']}")
            return

        print(f"📁 数据库文件：{DATABASE_PATH}")
        print(f"📊 总表数量：{db_info['total_tables']}")
        print(f"✅ 完整性检查：{'通过' if db_info['is_complete'] else '失败'}")

        if db_info['missing_tables']:
            print(f"❌ 缺失表：{', '.join(db_info['missing_tables'])}")

        print(f"\n📋 表记录统计：")
        for table, count in db_info['table_counts'].items():
            print(f"   {table}: {count} 条记录")

        print(f"\n🔄 异步机制表：")
        async_tables = ['trade_task_queue', 'trade_orders', 'trade_execution_log', 'order_status_history']
        for table in async_tables:
            if table in db_info['table_counts']:
                print(f"   ✅ {table}: {db_info['table_counts'][table]} 条记录")
            else:
                print(f"   ❌ {table}: 不存在")

        print("=" * 60)

    except Exception as e:
        print(f"❌ 打印数据库状态失败：{str(e)}")


def database_maintenance():
    """
    数据库维护函数
    包含常用的数据库管理操作
    """
    try:
        print("\n🔧 激进版策略数据库维护工具")
        print("=" * 50)

        # 检查数据库状态
        db_info = get_database_info()

        if "error" in db_info:
            print(f"❌ 数据库检查失败：{db_info['error']}")
            return False

        # 如果缺少异步机制表，自动迁移
        if not db_info['is_complete']:
            print("🔄 检测到数据库需要升级，开始自动迁移...")
            if migrate_database_to_async():
                print("✅ 数据库迁移成功")
            else:
                print("❌ 数据库迁移失败")
                return False

        # 打印状态
        print_database_status()

        return True

    except Exception as e:
        print(f"❌ 数据库维护失败：{str(e)}")
        return False


def load_strategy_status():
    """加载策略状态"""
    global g_strategy_status

    try:
        if g_db_connection is None:
            raise Exception("数据库连接为空")

        cursor = g_db_connection.cursor()
        cursor.execute("SELECT * FROM strategy_status ORDER BY id DESC LIMIT 1")
        result = cursor.fetchone()

        if result:
            g_strategy_status = {
                'id': result[0],
                'current_phase': result[1],
                'last_check_time': result[2],
                'created_time': result[3],
                'updated_time': result[4]
            }
        else:
            # 创建默认状态
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            g_strategy_status = {
                'id': None,
                'current_phase': 'sleeping',
                'last_check_time': current_time,
                'created_time': current_time,
                'updated_time': current_time
            }

    except Exception as e:
        error_msg = f"加载策略状态失败：{str(e)}"
        print(error_msg)
        raise e


def log_message(log_type: str, operation: str, message: str, details: Optional[Dict] = None, ContextInfo=None):
    """记录日志消息到数据库"""
    try:
        if g_db_connection is None:
            print(f"[{log_type}] {operation}: {message}")
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        details_json = json.dumps(details, ensure_ascii=False) if details else None

        # 获取K线日期和回测模式标识
        kline_date = current_time
        is_backtest = 0

        if ContextInfo:
            try:
                is_backtest = 1 if is_backtest_mode(ContextInfo) else 0
                if is_backtest:
                    kline_time = get_current_time(ContextInfo)
                    kline_date = kline_time.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                print(f"获取K线时间失败：{str(e)}")

        cursor.execute("""
            INSERT INTO trade_logs (log_date, kline_date, log_type, operation, message, details, is_backtest, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, kline_date, log_type, operation, message, details_json, is_backtest, current_time))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录日志失败：{str(e)}")


# ==================== 交易相关函数 ====================

def get_account_info(ContextInfo) -> Dict:
    """
    获取账户信息
    查询账户资金、持仓、融资额度等信息

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 账户信息
    """
    try:
        # 使用get_trade_detail_data获取账户资金信息
        account_data = get_trade_detail_data(
            ACCOUNT_ID,
            ACCOUNT_TYPE,
            'ACCOUNT'
        )
        
        if account_data and len(account_data) > 0:
            account = account_data[0]
            # print(f'account_data ===> ')
            # print(vars(account))
            # 获取基本资金信息
            total_assets = getattr(account, 'm_dBalance', 0)  # 总资产
            available_cash = getattr(account, 'm_dAvailable', 0)  # 可用资金
            # 获取融资信息（如果是信用账户）
            credit_limit = getattr(account, 'm_dFinMaxQuota', 0)  # 融资额度
            credit_available = getattr(account, 'm_dFinEnableQuota', 0)  # 可用融资

            account_info = {
                'total_assets': total_assets,
                'available_cash': available_cash,
                'credit_limit': credit_limit,
                'credit_available': credit_available
            }

            return account_info
        else:
            log_message("WARNING", "账户信息", "无法获取账户数据", None, ContextInfo)
            return {
                'total_assets': 0,
                'available_cash': 0,
                'credit_limit': 0,
                'credit_available': 0,
                'positions': {}
            }

    except Exception as e:
        log_message("ERROR", "账户信息", f"获取账户信息失败：{str(e)}", None, ContextInfo)
        return {
            'total_assets': 0,
            'available_cash': 0,
            'credit_limit': 0,
            'credit_available': 0,
            'positions': {}
        }



def get_current_price(ContextInfo, stock_code):
    """获取股票当前价格"""
    try:
        market_data = ContextInfo.get_market_data_ex(
            fields=['close'],
            stock_code=[stock_code],
            period='1min',
            count=1,
            dividend_type='front',
            fill_data=True
        )

        if market_data and stock_code in market_data:
            stock_data = market_data[stock_code]
            if len(stock_data) > 0:
                return float(stock_data['close'].iloc[-1] if hasattr(stock_data['close'], 'iloc') else stock_data['close'][-1])

        return None

    except Exception as e:
        log_message("ERROR", "价格获取", f"获取{stock_code}价格失败：{str(e)}", None, ContextInfo)
        return None


def place_buy_order(ContextInfo, stock_code, shares, reason, use_margin=False):
    """下买单（现金或融资）"""
    try:
        order_uuid = str(uuid.uuid4())
        log_message("INFO", "下单", f"准备买入{shares}股{stock_code}，原因：{reason}，融资：{use_margin}", None, ContextInfo)

        if not IQUANT_FUNCTIONS_AVAILABLE:
            # 模拟下单
            print(f"[模拟] 买入{shares}股{stock_code}，融资：{use_margin}")
            return True

        # 使用稳健版的正确参数格式
        if use_margin:
            # 融资买入
            passorder(
                27,  # 融资买入
                1101,
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '激进版策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo
            )
        else:
            # 现金买入
            passorder(
                33 if ACCOUNT_TYPE == "CREDIT" else 23,  # 买入
                1101,
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '激进版策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo
            )

        log_message("INFO", "下单", f"买入订单提交成功，UUID：{order_uuid}", None, ContextInfo)
        return f"TEMP_BUY_{order_uuid[:8]}"
    except Exception as e:
        log_message("ERROR", "下单", f"买入下单失败：{str(e)}", None, ContextInfo)
        return None


def place_sell_order(ContextInfo, stock_code, shares, reason):
    """下卖单"""
    try:
        order_uuid = str(uuid.uuid4())
        log_message("INFO", "下单", f"准备卖出{shares}股{stock_code}，原因：{reason}", None, ContextInfo)

        if not IQUANT_FUNCTIONS_AVAILABLE:
            # 模拟下单
            print(f"[模拟] 卖出{shares}股{stock_code}")
            return True

        # 使用稳健版的正确参数格式
        passorder(
            34 if ACCOUNT_TYPE == "CREDIT" else 24,  # 卖出
            1101,
            ACCOUNT_ID,
            stock_code,
            44,  # 市价
            -1,  # 市价
            shares,
            '激进版策略',
            1,
            order_uuid,  # 使用UUID作为备注，用于回调匹配
            ContextInfo
        )

        log_message("INFO", "下单", f"卖出订单提交成功，UUID：{order_uuid}", None, ContextInfo)
        return f"TEMP_SELL_{order_uuid[:8]}"
    except Exception as e:
        log_message("ERROR", "下单", f"卖出下单失败：{str(e)}", None, ContextInfo)
        return None


def record_trade_to_db(trade_type, stock_code, shares, amount, signal_info, ContextInfo):
    """记录交易到数据库"""
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 计算价格
        price = amount / shares if shares > 0 else 0

        cursor.execute("""
            INSERT INTO trade_records
            (trade_date, trade_type, stock_code, shares, price, amount,
             signal_date, signal_type, trade_reason, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            current_time,
            trade_type,
            stock_code,
            shares,
            price,
            amount,
            signal_info.get('signal_date') if signal_info else None,
            signal_info.get('signal_type') if signal_info else None,
            f"激进版{trade_type}",
            current_time
        ))

        # 更新持仓记录
        update_holdings_in_db(stock_code, trade_type, shares, price)

        g_db_connection.commit()
        log_message("INFO", "交易记录", f"记录{trade_type}交易到数据库：{shares}股{stock_code}", None, ContextInfo)

    except Exception as e:
        log_message("ERROR", "交易记录", f"记录交易到数据库失败：{str(e)}", None, ContextInfo)


def update_holdings_in_db(stock_code, trade_type, shares, price):
    """更新数据库中的持仓记录"""
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 查询当前持仓
        cursor.execute("SELECT shares, avg_cost, total_cost FROM holdings WHERE stock_code = ?", (stock_code,))
        result = cursor.fetchone()

        if result:
            current_shares, current_avg_cost, current_total_cost = result
        else:
            current_shares, current_avg_cost, current_total_cost = 0, 0, 0

        if trade_type == "BUY":
            # 买入：增加持仓
            new_shares = current_shares + shares
            new_total_cost = current_total_cost + (shares * price)
            new_avg_cost = new_total_cost / new_shares if new_shares > 0 else 0
        else:
            # 卖出：减少持仓
            new_shares = current_shares - shares
            if new_shares <= 0:
                new_shares = 0
                new_avg_cost = 0
                new_total_cost = 0
            else:
                new_avg_cost = current_avg_cost  # 卖出不改变平均成本
                new_total_cost = new_shares * new_avg_cost

        # 更新或插入持仓记录
        if result:
            cursor.execute("""
                UPDATE holdings
                SET shares = ?, avg_cost = ?, total_cost = ?, last_update = ?
                WHERE stock_code = ?
            """, (new_shares, new_avg_cost, new_total_cost, current_time, stock_code))
        else:
            cursor.execute("""
                INSERT INTO holdings (stock_code, shares, avg_cost, total_cost, last_update)
                VALUES (?, ?, ?, ?, ?)
            """, (stock_code, new_shares, new_avg_cost, new_total_cost, current_time))

    except Exception as e:
        print(f"更新持仓记录失败：{str(e)}")


def get_holdings_from_db(stock_code):
    """从数据库获取持仓信息"""
    try:
        if g_db_connection is None:
            return None

        cursor = g_db_connection.cursor()
        cursor.execute("SELECT shares, avg_cost, total_cost FROM holdings WHERE stock_code = ?", (stock_code,))
        result = cursor.fetchone()

        if result:
            return {
                'shares': result[0],
                'avg_cost': result[1],
                'total_cost': result[2]
            }
        else:
            return {'shares': 0, 'avg_cost': 0, 'total_cost': 0}

    except Exception as e:
        print(f"获取持仓信息失败：{str(e)}")
        return None


# ==================== 辅助函数 ====================

def down_history_data(stock_code, period, start_date, end_date):
    """
    下载历史数据（模拟函数）

    Args:
        stock_code: 股票代码
        period: 周期
        start_date: 开始日期
        end_date: 结束日期
    """
    try:
        # 在实际环境中，这里会调用iQuant的数据下载函数
        # 这里只是一个模拟实现
        print(f"[模拟] 下载{stock_code}的{period}数据，从{start_date}到{end_date}")
        return True
    except Exception as e:
        print(f"下载历史数据失败：{str(e)}")
        return False


def get_strategy_performance_summary():
    """
    获取策略表现摘要

    Returns:
        dict: 策略表现统计
    """
    try:
        if g_db_connection is None:
            return {
                'total_trades': 0,
                'successful_trades': 0,
                'failed_trades': 0,
                'total_signals': 0,
                'valid_signals': 0,
                'filtered_signals': 0,
                'current_phase': 'unknown'
            }

        cursor = g_db_connection.cursor()

        # 统计交易次数
        cursor.execute("SELECT COUNT(*) FROM trade_records")
        total_trades = cursor.fetchone()[0] or 0

        # 统计信号次数
        cursor.execute("SELECT COUNT(*) FROM signal_history")
        total_signals = cursor.fetchone()[0] or 0

        cursor.execute("SELECT COUNT(*) FROM signal_history WHERE is_valid = 1")
        valid_signals = cursor.fetchone()[0] or 0

        # 获取当前阶段
        current_phase = g_strategy_status.get('current_phase', 'unknown') if g_strategy_status else 'unknown'

        return {
            'total_trades': total_trades,
            'successful_trades': total_trades,  # 简化统计
            'failed_trades': 0,
            'total_signals': total_signals,
            'valid_signals': valid_signals,
            'filtered_signals': total_signals - valid_signals,
            'current_phase': current_phase
        }

    except Exception as e:
        print(f"获取策略表现摘要失败：{str(e)}")
        return {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_signals': 0,
            'valid_signals': 0,
            'filtered_signals': 0,
            'current_phase': 'unknown'
        }


def validate_strategy_state():
    """
    验证策略状态是否正常

    Returns:
        bool: 状态是否正常
    """
    try:
        if g_strategy_status is None:
            return False

        if g_db_connection is None:
            return False

        # 检查必要的字段
        required_fields = ['current_phase', 'last_check_time']
        for field in required_fields:
            if field not in g_strategy_status:
                return False

        return True

    except Exception as e:
        print(f"验证策略状态失败：{str(e)}")
        return False


# ==================== 异步回调处理器 ====================

class TradeTaskCallbackHandler:
    """交易任务回调处理器"""

    def __init__(self):
        self.task_queue = g_trade_task_queue

    def find_task_by_order_uuid(self, order_uuid: str, expected_status: str = None) -> Optional[Dict]:
        """根据订单UUID查找任务"""
        try:
            cursor = g_db_connection.cursor()

            if expected_status:
                cursor.execute("""
                    SELECT id, task_group_id, task_type, stock_code, target_shares, task_status
                    FROM trade_task_queue
                    WHERE order_uuid = ? AND task_status = ?
                """, (order_uuid, expected_status))
            else:
                cursor.execute("""
                    SELECT id, task_group_id, task_type, stock_code, target_shares, task_status
                    FROM trade_task_queue
                    WHERE order_uuid = ?
                """, (order_uuid,))

            result = cursor.fetchone()
            if result:
                return {
                    'id': result[0],
                    'task_group_id': result[1],
                    'task_type': result[2],
                    'stock_code': result[3],
                    'target_shares': result[4],
                    'task_status': result[5]
                }
            return None

        except Exception as e:
            print(f"查找任务失败：{str(e)}")
            return None

    def handle_order_callback(self, orderInfo):
        """处理订单回调"""
        try:
            # 获取订单UUID（从备注字段）
            order_uuid = str(getattr(orderInfo, 'm_strRemark', ''))
            if not order_uuid:
                print("❌ 无法获取订单UUID（m_strRemark为空）")
                return

            # 获取订单ID
            order_id = str(getattr(orderInfo, 'm_strOrderSysID', ''))
            order_status = getattr(orderInfo, 'm_nOrderStatus', 0)
            instrument_id = str(getattr(orderInfo, 'm_strInstrumentID', ''))
            volume_traded = getattr(orderInfo, 'm_nVolumeTraded', 0)
            volume_total = getattr(orderInfo, 'm_nVolumeTotalOriginal', 0)

            # 订单状态映射
            status_map = {
                48: "未报", 49: "待报", 50: "已报", 51: "已报待撤", 52: "部成待撤",
                53: "部撤", 54: "已撤", 55: "部成", 56: "已成", 57: "废单"
            }
            status_desc = status_map.get(order_status, f"未知状态({order_status})")

            print(f"收到订单回调：UUID={order_uuid}，订单ID={order_id}，状态={order_status}({status_desc})，股票={instrument_id}，成交量={volume_traded}/{volume_total}")

            # 根据UUID查找对应的任务
            task = self.find_task_by_order_uuid(order_uuid, TaskStatus.WAITING_CALLBACK.value)

            if task:
                self.process_order_callback_with_uuid(task, orderInfo, order_uuid, order_id, status_desc)
            else:
                print(f"⚠️ 未找到匹配的任务：UUID={order_uuid}")

        except Exception as e:
            error_msg = f"处理订单回调失败：{str(e)}"
            print(error_msg)

    def handle_deal_callback(self, dealInfo):
        """处理成交回调"""
        try:
            # 获取订单UUID（从备注字段）
            order_uuid = str(getattr(dealInfo, 'm_strRemark', ''))
            if not order_uuid:
                print("❌ 无法获取订单UUID（m_strRemark为空）")
                return

            # 获取订单ID
            order_id = None
            possible_id_attrs = ['m_strOrderSysID', 'm_strOrderID', 'm_strOrderRef', 'm_nOrderRef']
            for attr in possible_id_attrs:
                try:
                    temp_id = str(getattr(dealInfo, attr, ''))
                    if temp_id and temp_id != '' and temp_id != '0':
                        order_id = temp_id
                        break
                except:
                    continue

            if not order_id:
                print("❌ 无法获取有效的订单ID")
                return

            # 根据UUID查找对应的任务
            task = self.find_task_by_order_uuid(order_uuid, TaskStatus.COMPLETED.value)

            if task:
                self.process_deal_callback_with_uuid(task, dealInfo, order_uuid, order_id)
            else:
                print(f"⚠️ 未找到匹配的任务：UUID={order_uuid}")

        except Exception as e:
            error_msg = f"处理成交回调失败：{str(e)}"
            print(error_msg)

    def process_order_callback_with_uuid(self, task: Dict, orderInfo, order_uuid: str, order_id: str, status_desc: str):
        """处理带UUID的订单回调"""
        try:
            task_id = task['id']
            task_group_id = task['task_group_id']
            order_status = getattr(orderInfo, 'm_nOrderStatus', 0)
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 更新任务中的真实订单ID
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue
                SET order_id = ?
                WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
            """, (order_id, order_uuid))

            print(f"✓ 订单状态更新：UUID={order_uuid}，状态={status_desc}")

            # 处理订单最终状态
            if order_status == 56:  # 已成交
                # 更新任务状态为已完成
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = 'COMPLETED'
                    WHERE id = ?
                """, (task_id,))
                print(f"✓ 任务已完成：task_id={task_id}")

            elif order_status in [54, 57]:  # 已撤、废单
                error_msg = f"订单失败：{status_desc}"
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = 'FAILED', error_message = ?
                    WHERE id = ?
                """, (error_msg, task_id))
                print(f"❌ 任务失败：task_id={task_id}，原因：{error_msg}")

            g_db_connection.commit()

        except Exception as e:
            print(f"❌ 处理订单回调失败：{str(e)}")

    def process_deal_callback_with_uuid(self, task: Dict, dealInfo, order_uuid: str, order_id: str):
        """处理带UUID的成交回调"""
        try:
            task_id = task['id']
            task_group_id = task['task_group_id']
            task_type = task['task_type']

            print(f"🔄 开始处理成交回调：UUID={order_uuid}, 订单ID={order_id}")

            # 获取成交信息
            deal_price = float(getattr(dealInfo, 'm_dPrice', 0))
            deal_volume = int(getattr(dealInfo, 'm_nVolume', 0))
            deal_amount = deal_price * deal_volume

            print(f"📊 成交详情：价格={deal_price:.4f}，数量={deal_volume}，金额={deal_amount:.2f}")

            # 记录成交到数据库
            self.record_deal_execution(order_uuid, task_type, task['stock_code'],
                                     deal_volume, deal_price, deal_amount, order_id)

            # === 重要：激进版交易成功后更新防重复标记 ===
            self.update_aggressive_trade_status_after_success(task_id, task_type, task_group_id)

            print(f"✅ 成交回调处理完成：任务ID={task_id}")

        except Exception as e:
            print(f"❌ 处理成交回调失败：{str(e)}")

    def record_deal_execution(self, order_uuid: str, task_type: str, stock_code: str,
                            shares: int, price: float, amount: float, order_id: str):
        """记录成交执行详情"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            trade_type = "BUY" if "BUY" in task_type else "SELL"

            cursor.execute("""
                INSERT INTO trade_execution_log
                (order_uuid, order_id, trade_type, stock_code, shares, price, amount,
                 commission, stamp_tax, transfer_fee, net_amount, status, trade_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (order_uuid, order_id, trade_type, stock_code, shares, price, amount,
                  0, 0, 0, amount, 'SUCCESS', current_time))  # 简化费用计算

            g_db_connection.commit()
            print(f"✓ 成交记录已保存：{trade_type} {stock_code} {shares}股")

        except Exception as e:
            print(f"❌ 记录成交执行失败：{str(e)}")

    def update_aggressive_trade_status_after_success(self, task_id: int, task_type: str, task_group_id: str):
        """
        激进版交易成功后更新状态（防重复交易的关键）

        Args:
            task_id: 任务ID
            task_type: 任务类型
            task_group_id: 任务组ID
        """
        try:
            # 更新策略状态中的最后交易时间
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE strategy_status
                SET last_check_time = ?, updated_time = ?
                WHERE id = (SELECT MAX(id) FROM strategy_status)
            """, (current_time, current_time))

            g_db_connection.commit()

            print(f"🔄 激进版交易成功，已更新策略状态：任务ID={task_id}，任务类型={task_type}")

            # 记录状态更新日志
            log_message("INFO", "状态更新",
                       f"激进版交易成功后更新状态：任务ID={task_id}，任务类型={task_type}，"
                       f"任务组={task_group_id}，时间={current_time}", None, None)

        except Exception as e:
            print(f"❌ 更新激进版交易状态失败：{str(e)}")


# 全局回调处理器实例
g_trade_task_callback_handler = TradeTaskCallbackHandler()


# ==================== iQuant 标准回调函数 ====================

def order_callback(ContextInfo, orderInfo):
    """
    iQuant 订单回调函数
    当订单状态发生变化时，iQuant 会自动调用此函数

    Args:
        ContextInfo: iQuant上下文信息对象
        orderInfo: 订单信息对象
    """
    try:
        # 调用任务队列的订单回调处理
        g_trade_task_callback_handler.handle_order_callback(orderInfo)

    except Exception as e:
        error_msg = f"订单回调处理失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "订单回调", error_msg, None, ContextInfo)


def deal_callback(ContextInfo, dealInfo):
    """
    iQuant 成交回调函数
    当订单成交时，iQuant 会自动调用此函数

    Args:
        ContextInfo: iQuant上下文信息对象
        dealInfo: 成交信息对象
    """
    try:
        # 调用任务队列的成交回调处理
        g_trade_task_callback_handler.handle_deal_callback(dealInfo)

    except Exception as e:
        error_msg = f"成交回调处理失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "成交回调", error_msg, None, ContextInfo)


def orderError_callback(ContextInfo, orderArgs, errMsg):
    """
    iQuant 下单错误回调函数
    当下单错误时，iQuant 会自动调用此函数

    Args:
        ContextInfo: iQuant上下文信息对象
        orderArgs: 下单参数
        errMsg: 错误信息
    """
    try:
        print(f"【下单错误回调】错误信息：{errMsg}")

        # 提取订单UUID
        strategy_name = getattr(orderArgs, 'stragegyName', '')
        if '_&&&_' in strategy_name:
            order_uuid = strategy_name.split('_&&&_')[1]
        else:
            order_uuid = strategy_name

        if not order_uuid:
            print("【下单错误回调】警告：无法提取订单UUID，跳过状态更新")
            return

        # 更新任务状态为失败
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            UPDATE trade_task_queue
            SET task_status = 'FAILED', error_message = ?
            WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
        """, (str(errMsg), order_uuid))

        cursor.execute("""
            UPDATE trade_orders
            SET order_status = 'FAILED', error_message = ?
            WHERE order_uuid = ?
        """, (str(errMsg), order_uuid))

        g_db_connection.commit()

        print(f"【下单错误回调】处理完成：UUID={order_uuid}")
        log_message("ERROR", "下单错误", f"订单UUID={order_uuid}，错误信息={errMsg}", None, ContextInfo)

    except Exception as e:
        error_msg = f"下单错误回调处理失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "下单错误回调", error_msg, None, ContextInfo)
