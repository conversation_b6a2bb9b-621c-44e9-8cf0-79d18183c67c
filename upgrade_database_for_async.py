# -*- coding: utf-8 -*-
"""
数据库升级脚本：为异步机制添加缺失的表
用于升级现有的激进版策略数据库，添加异步交易和防重复下单所需的表
"""

import sqlite3
import datetime
import os

# 数据库路径
DATABASE_PATH = "aggressive_strategy.db"

def check_table_exists(cursor, table_name: str) -> bool:
    """检查表是否存在"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None

def upgrade_database():
    """升级数据库，添加异步机制所需的表"""
    try:
        print("开始升级激进版策略数据库...")
        
        # 连接数据库
        if not os.path.exists(DATABASE_PATH):
            print(f"数据库文件不存在：{DATABASE_PATH}")
            print("请先运行激进版策略初始化数据库")
            return False
            
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        # 检查并创建缺失的表
        tables_to_create = []
        
        # 1. 检查交易订单表
        if not check_table_exists(cursor, 'trade_orders'):
            tables_to_create.append('trade_orders')
            cursor.execute("""
                CREATE TABLE trade_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_date TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    order_type TEXT NOT NULL,
                    order_reason TEXT NOT NULL,
                    target_shares INTEGER NOT NULL,
                    actual_shares INTEGER DEFAULT 0,
                    actual_price REAL DEFAULT 0,
                    order_status TEXT NOT NULL,
                    order_uuid TEXT,
                    order_id TEXT,
                    error_message TEXT,
                    created_time TEXT NOT NULL
                )
            """)
            print("✅ 创建 trade_orders 表")

        # 2. 检查交易任务队列表
        if not check_table_exists(cursor, 'trade_task_queue'):
            tables_to_create.append('trade_task_queue')
            cursor.execute("""
                CREATE TABLE trade_task_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_group_id TEXT NOT NULL,
                    task_type TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    target_shares INTEGER NOT NULL,
                    target_amount REAL,
                    estimated_price REAL,
                    estimated_fees REAL,
                    task_status TEXT NOT NULL,
                    depends_on_task TEXT,
                    order_uuid TEXT UNIQUE,
                    order_id TEXT,
                    task_params TEXT,
                    error_message TEXT,
                    created_time TEXT NOT NULL,
                    updated_time TEXT
                )
            """)
            print("✅ 创建 trade_task_queue 表")

        # 3. 检查交易执行日志表
        if not check_table_exists(cursor, 'trade_execution_log'):
            tables_to_create.append('trade_execution_log')
            cursor.execute("""
                CREATE TABLE trade_execution_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_time TEXT NOT NULL,
                    trade_type TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    shares INTEGER NOT NULL,
                    price REAL NOT NULL,
                    amount REAL NOT NULL,
                    commission REAL DEFAULT 0,
                    stamp_tax REAL DEFAULT 0,
                    transfer_fee REAL DEFAULT 0,
                    net_amount REAL NOT NULL,
                    fees REAL DEFAULT 0,
                    order_id TEXT,
                    order_uuid TEXT,
                    status TEXT NOT NULL,
                    created_time TEXT NOT NULL
                )
            """)
            print("✅ 创建 trade_execution_log 表")

        # 4. 检查任务日志表
        if not check_table_exists(cursor, 'trade_task_log'):
            tables_to_create.append('trade_task_log')
            cursor.execute("""
                CREATE TABLE trade_task_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER,
                    task_group_id TEXT,
                    log_level TEXT NOT NULL,
                    log_category TEXT NOT NULL,
                    log_message TEXT NOT NULL,
                    extra_data TEXT,
                    log_time TEXT NOT NULL
                )
            """)
            print("✅ 创建 trade_task_log 表")

        # 5. 检查订单状态历史表
        if not check_table_exists(cursor, 'order_status_history'):
            tables_to_create.append('order_status_history')
            cursor.execute("""
                CREATE TABLE order_status_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_uuid TEXT NOT NULL,
                    order_id TEXT,
                    stock_code TEXT,
                    order_status INTEGER,
                    status_desc TEXT,
                    volume_traded INTEGER DEFAULT 0,
                    volume_total INTEGER DEFAULT 0,
                    callback_time TEXT NOT NULL,
                    created_time TEXT NOT NULL
                )
            """)
            print("✅ 创建 order_status_history 表")

        # 6. 检查持仓记录表（增强版）
        if not check_table_exists(cursor, 'position_records'):
            tables_to_create.append('position_records')
            cursor.execute("""
                CREATE TABLE position_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    record_date TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    shares INTEGER NOT NULL,
                    avg_cost REAL NOT NULL,
                    market_value REAL NOT NULL,
                    current_price REAL NOT NULL,
                    period_number INTEGER,
                    target_value REAL,
                    created_time TEXT NOT NULL
                )
            """)
            print("✅ 创建 position_records 表")

        # 7. 检查账户快照表
        if not check_table_exists(cursor, 'account_snapshot'):
            tables_to_create.append('account_snapshot')
            cursor.execute("""
                CREATE TABLE account_snapshot (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_group_id TEXT,
                    snapshot_point TEXT NOT NULL,
                    available_cash REAL DEFAULT 0,
                    margin_available REAL DEFAULT 0,
                    stock_510720_shares INTEGER DEFAULT 0,
                    stock_510720_value REAL DEFAULT 0,
                    stock_159915_shares INTEGER DEFAULT 0,
                    stock_159915_value REAL DEFAULT 0,
                    snapshot_time TEXT NOT NULL
                )
            """)
            print("✅ 创建 account_snapshot 表")

        # 创建新的索引
        new_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_trade_orders_date ON trade_orders(order_date)",
            "CREATE INDEX IF NOT EXISTS idx_trade_orders_uuid ON trade_orders(order_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_trade_task_queue_uuid ON trade_task_queue(order_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_trade_task_queue_group ON trade_task_queue(task_group_id)",
            "CREATE INDEX IF NOT EXISTS idx_trade_execution_log_time ON trade_execution_log(trade_time)",
            "CREATE INDEX IF NOT EXISTS idx_trade_execution_log_uuid ON trade_execution_log(order_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_position_records_date ON position_records(record_date)",
            "CREATE INDEX IF NOT EXISTS idx_position_records_code ON position_records(stock_code)",
            "CREATE INDEX IF NOT EXISTS idx_order_status_history_uuid ON order_status_history(order_uuid)"
        ]
        
        # 创建原有索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_date ON signal_history(signal_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_records_date ON trade_records(trade_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_logs_date ON trade_logs(log_date)")
        
        # 创建新索引
        for index_sql in new_indexes:
            cursor.execute(index_sql)
        
        connection.commit()
        
        if tables_to_create:
            print(f"\n🎉 数据库升级完成！新增了 {len(tables_to_create)} 个表：")
            for table in tables_to_create:
                print(f"   - {table}")
        else:
            print("\n✅ 数据库已是最新版本，无需升级")
            
        print(f"\n📊 数据库升级总结：")
        print(f"   - 异步交易任务队列表：trade_task_queue")
        print(f"   - 防重复下单检查表：trade_orders")
        print(f"   - 交易执行日志表：trade_execution_log")
        print(f"   - 订单状态历史表：order_status_history")
        print(f"   - 增强持仓记录表：position_records")
        print(f"   - 账户快照表：account_snapshot")
        print(f"   - 任务日志表：trade_task_log")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库升级失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("激进版策略数据库升级工具")
    print("=" * 50)
    
    success = upgrade_database()
    
    if success:
        print("\n🚀 数据库升级成功！现在可以使用异步机制和防重复下单功能了")
    else:
        print("\n💥 数据库升级失败！请检查错误信息")

if __name__ == "__main__":
    main()
