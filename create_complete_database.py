# -*- coding: utf-8 -*-
"""
创建完整的激进版策略数据库
包含所有异步机制和防重复下单所需的表
"""

import sqlite3
import datetime
import os

DATABASE_PATH = "aggressive_strategy.db"

def create_complete_database():
    """创建完整的数据库结构"""
    try:
        print("开始创建完整的激进版策略数据库...")
        
        # 删除现有数据库文件（如果存在）
        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)
            print(f"已删除现有数据库文件：{DATABASE_PATH}")
        
        # 创建新数据库
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        print("正在创建数据库表...")
        
        # 1. 信号历史表
        cursor.execute("""
            CREATE TABLE signal_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_date TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                signal_price REAL NOT NULL,
                ema_value REAL NOT NULL,
                bottom_line REAL,
                top_line REAL,
                kline_position INTEGER,
                kline_date TEXT,
                is_valid INTEGER NOT NULL,
                filter_reason TEXT,
                created_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 signal_history 表")

        # 2. 交易记录表
        cursor.execute("""
            CREATE TABLE trade_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trade_date TEXT NOT NULL,
                trade_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                shares INTEGER NOT NULL,
                price REAL NOT NULL,
                amount REAL NOT NULL,
                signal_date TEXT,
                signal_type TEXT,
                trade_reason TEXT,
                created_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 trade_records 表")

        # 3. 持仓记录表
        cursor.execute("""
            CREATE TABLE holdings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL UNIQUE,
                shares INTEGER NOT NULL,
                avg_cost REAL NOT NULL,
                total_cost REAL NOT NULL,
                last_update TEXT NOT NULL
            )
        """)
        print("✅ 创建 holdings 表")

        # 4. 交易日志表
        cursor.execute("""
            CREATE TABLE trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,
                kline_date TEXT,
                log_type TEXT NOT NULL,
                operation TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                is_backtest INTEGER DEFAULT 0,
                created_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 trade_logs 表")

        # 5. 策略状态表
        cursor.execute("""
            CREATE TABLE strategy_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                current_phase TEXT NOT NULL,
                last_check_time TEXT NOT NULL,
                created_time TEXT NOT NULL,
                updated_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 strategy_status 表")

        # === 异步机制相关表 ===

        # 6. 交易订单表（防重复下单核心）
        cursor.execute("""
            CREATE TABLE trade_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_date TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                order_type TEXT NOT NULL,
                order_reason TEXT NOT NULL,
                target_shares INTEGER NOT NULL,
                actual_shares INTEGER DEFAULT 0,
                actual_price REAL DEFAULT 0,
                order_status TEXT NOT NULL,
                order_uuid TEXT,
                order_id TEXT,
                error_message TEXT,
                created_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 trade_orders 表（防重复下单核心）")

        # 7. 交易任务队列表（异步机制核心）
        cursor.execute("""
            CREATE TABLE trade_task_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT NOT NULL,
                task_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                target_shares INTEGER NOT NULL,
                target_amount REAL,
                estimated_price REAL,
                estimated_fees REAL,
                task_status TEXT NOT NULL,
                depends_on_task TEXT,
                order_uuid TEXT UNIQUE,
                order_id TEXT,
                task_params TEXT,
                error_message TEXT,
                created_time TEXT NOT NULL,
                updated_time TEXT
            )
        """)
        print("✅ 创建 trade_task_queue 表（异步机制核心）")

        # 8. 交易执行日志表
        cursor.execute("""
            CREATE TABLE trade_execution_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trade_time TEXT NOT NULL,
                trade_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                shares INTEGER NOT NULL,
                price REAL NOT NULL,
                amount REAL NOT NULL,
                commission REAL DEFAULT 0,
                stamp_tax REAL DEFAULT 0,
                transfer_fee REAL DEFAULT 0,
                net_amount REAL NOT NULL,
                fees REAL DEFAULT 0,
                order_id TEXT,
                order_uuid TEXT,
                status TEXT NOT NULL,
                created_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 trade_execution_log 表")

        # 9. 任务日志表
        cursor.execute("""
            CREATE TABLE trade_task_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER,
                task_group_id TEXT,
                log_level TEXT NOT NULL,
                log_category TEXT NOT NULL,
                log_message TEXT NOT NULL,
                extra_data TEXT,
                log_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 trade_task_log 表")

        # 10. 订单状态历史表
        cursor.execute("""
            CREATE TABLE order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_uuid TEXT NOT NULL,
                order_id TEXT,
                stock_code TEXT,
                order_status INTEGER,
                status_desc TEXT,
                volume_traded INTEGER DEFAULT 0,
                volume_total INTEGER DEFAULT 0,
                callback_time TEXT NOT NULL,
                created_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 order_status_history 表")

        # 11. 增强持仓记录表
        cursor.execute("""
            CREATE TABLE position_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                record_date TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                shares INTEGER NOT NULL,
                avg_cost REAL NOT NULL,
                market_value REAL NOT NULL,
                current_price REAL NOT NULL,
                period_number INTEGER,
                target_value REAL,
                created_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 position_records 表")

        # 12. 账户快照表
        cursor.execute("""
            CREATE TABLE account_snapshot (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT,
                snapshot_point TEXT NOT NULL,
                available_cash REAL DEFAULT 0,
                margin_available REAL DEFAULT 0,
                stock_510720_shares INTEGER DEFAULT 0,
                stock_510720_value REAL DEFAULT 0,
                stock_159915_shares INTEGER DEFAULT 0,
                stock_159915_value REAL DEFAULT 0,
                snapshot_time TEXT NOT NULL
            )
        """)
        print("✅ 创建 account_snapshot 表")

        print("\n正在创建索引...")
        
        # 创建所有索引
        indexes = [
            "CREATE INDEX idx_signal_history_date ON signal_history(signal_date)",
            "CREATE INDEX idx_trade_records_date ON trade_records(trade_date)",
            "CREATE INDEX idx_trade_logs_date ON trade_logs(log_date)",
            "CREATE INDEX idx_trade_orders_date ON trade_orders(order_date)",
            "CREATE INDEX idx_trade_orders_uuid ON trade_orders(order_uuid)",
            "CREATE INDEX idx_trade_task_queue_uuid ON trade_task_queue(order_uuid)",
            "CREATE INDEX idx_trade_task_queue_group ON trade_task_queue(task_group_id)",
            "CREATE INDEX idx_trade_execution_log_time ON trade_execution_log(trade_time)",
            "CREATE INDEX idx_trade_execution_log_uuid ON trade_execution_log(order_uuid)",
            "CREATE INDEX idx_position_records_date ON position_records(record_date)",
            "CREATE INDEX idx_position_records_code ON position_records(stock_code)",
            "CREATE INDEX idx_order_status_history_uuid ON order_status_history(order_uuid)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        connection.commit()
        connection.close()
        
        print(f"\n🎉 完整数据库创建成功！")
        print(f"📁 数据库文件：{DATABASE_PATH}")
        print(f"📊 总共创建了 12 个表和 {len(indexes)} 个索引")
        print(f"\n核心表说明：")
        print(f"   🔄 trade_task_queue - 异步任务队列")
        print(f"   🚫 trade_orders - 防重复下单检查")
        print(f"   📝 trade_execution_log - 交易执行记录")
        print(f"   📞 order_status_history - 订单状态历史")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    create_complete_database()
