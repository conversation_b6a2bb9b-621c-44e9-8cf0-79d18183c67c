# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3
import os

DATABASE_PATH = "aggressive_strategy.db"

def check_database():
    """检查数据库表结构"""
    try:
        if not os.path.exists(DATABASE_PATH):
            print(f"数据库文件不存在：{DATABASE_PATH}")
            return
            
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"数据库文件：{DATABASE_PATH}")
        print(f"现有表数量：{len(tables)}")
        print("\n现有表列表：")
        
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"    列数：{len(columns)}")
            for col in columns:
                print(f"      {col[1]} ({col[2]})")
            print()
        
        # 检查需要的表是否存在
        required_tables = [
            'trade_orders',
            'trade_task_queue', 
            'trade_execution_log',
            'order_status_history',
            'position_records',
            'account_snapshot',
            'trade_task_log'
        ]
        
        existing_table_names = [table[0] for table in tables]
        missing_tables = [table for table in required_tables if table not in existing_table_names]
        
        if missing_tables:
            print(f"❌ 缺少以下表（异步机制需要）：")
            for table in missing_tables:
                print(f"   - {table}")
        else:
            print("✅ 所有异步机制需要的表都已存在")
            
        connection.close()
        
    except Exception as e:
        print(f"检查数据库失败：{str(e)}")

if __name__ == "__main__":
    check_database()
