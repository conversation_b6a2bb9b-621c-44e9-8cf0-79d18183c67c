# -*- coding: utf-8 -*-
"""
激进版策略测试文件
用于测试激进版策略的基本功能
"""

import sys
import os
import datetime
import sqlite3

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入激进版策略
import aggressive_strategy as strategy

class MockContextInfo:
    """模拟ContextInfo对象"""
    
    def __init__(self):
        self.barpos = 100
        self._universe = []
        self._account = None
        
    def set_universe(self, stocks):
        self._universe = stocks
        print(f"设置股票池：{stocks}")
        
    def set_account(self, account_id):
        self._account = account_id
        print(f"设置账户：{account_id}")
        
    def get_bar_timetag(self, barpos):
        # 模拟返回时间戳
        base_time = datetime.datetime(2024, 1, 1)
        return int((base_time + datetime.timedelta(days=barpos)).timestamp() * 1000)
        
    def is_last_bar(self):
        return True
        
    def get_market_data_ex(self, **kwargs):
        """模拟市场数据"""
        stock_code = kwargs.get('stock_code', [])[0] if kwargs.get('stock_code') else None
        count = kwargs.get('count', 100)
        
        if not stock_code:
            return None
            
        # 生成模拟数据
        import pandas as pd
        dates = pd.date_range(start='2020-01-01', periods=count, freq='D')
        
        # 模拟价格数据（创建一个有趋势的价格序列）
        base_price = 2.5
        prices = []
        for i in range(count):
            # 添加一些随机波动和趋势
            trend = 0.001 * i  # 轻微上升趋势
            noise = (i % 10 - 5) * 0.01  # 周期性波动
            price = base_price + trend + noise
            prices.append(max(price, 0.1))  # 确保价格为正
            
        data = pd.DataFrame({
            'open': [p * 0.99 for p in prices],
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'close': prices
        }, index=dates)
        
        return {stock_code: data}


def test_database_initialization():
    """测试数据库初始化"""
    print("\n=== 测试数据库初始化 ===")
    try:
        # 删除测试数据库文件（如果存在）
        if os.path.exists("test_aggressive_strategy.db"):
            os.remove("test_aggressive_strategy.db")
            
        # 临时修改数据库路径
        original_db_path = strategy.DATABASE_PATH
        strategy.DATABASE_PATH = "test_aggressive_strategy.db"
        
        # 初始化数据库
        strategy.init_database()
        
        # 检查数据库是否创建成功
        if os.path.exists("test_aggressive_strategy.db"):
            print("✅ 数据库初始化成功")
            
            # 检查表是否创建
            conn = sqlite3.connect("test_aggressive_strategy.db")
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            expected_tables = ['signal_history', 'trade_records', 'holdings', 'trade_logs', 'strategy_status']
            for table in expected_tables:
                if table in tables:
                    print(f"✅ 表 {table} 创建成功")
                else:
                    print(f"❌ 表 {table} 创建失败")
        else:
            print("❌ 数据库文件创建失败")
            
        # 恢复原始数据库路径
        strategy.DATABASE_PATH = original_db_path
        
    except Exception as e:
        print(f"❌ 数据库初始化测试失败：{str(e)}")


def test_technical_indicators():
    """测试技术指标计算"""
    print("\n=== 测试技术指标计算 ===")
    try:
        # 创建模拟ContextInfo
        context = MockContextInfo()
        
        # 设置全局时间
        strategy.g_current_bar_time = datetime.datetime.now()
        
        # 测试技术指标更新
        technical_data = strategy.update_technical_indicators(context)
        
        if technical_data:
            print("✅ 技术指标计算成功")
            print(f"当前期EMA: {technical_data['current']['ema_value']:.3f}")
            print(f"当前期底部线: {technical_data['current']['bottom_line']:.3f}")
            print(f"当前期顶部线: {technical_data['current']['top_line']:.3f}")
            print(f"当前期收盘价: {technical_data['current']['close']:.3f}")
        else:
            print("❌ 技术指标计算失败")
            
    except Exception as e:
        print(f"❌ 技术指标测试失败：{str(e)}")


def test_signal_detection():
    """测试信号检测"""
    print("\n=== 测试信号检测 ===")
    try:
        # 创建模拟ContextInfo
        context = MockContextInfo()
        
        # 设置全局时间
        strategy.g_current_bar_time = datetime.datetime.now()
        
        # 初始化数据库连接
        strategy.init_database()
        strategy.load_strategy_status()
        
        # 测试信号检测
        signal_result = strategy.detect_latest_signal(context)
        
        if signal_result:
            print("✅ 信号检测成功")
            print(f"当前阶段: {signal_result['current_phase']}")
            if signal_result['latest_signal']:
                print(f"最新信号: {signal_result['latest_signal']['signal_type']}")
            else:
                print("最新信号: 无")
        else:
            print("❌ 信号检测失败")
            
    except Exception as e:
        print(f"❌ 信号检测测试失败：{str(e)}")


def test_strategy_initialization():
    """测试策略初始化"""
    print("\n=== 测试策略初始化 ===")
    try:
        # 创建模拟ContextInfo
        context = MockContextInfo()

        # 测试策略初始化
        strategy.init(context)
        print("✅ 策略初始化成功")

    except Exception as e:
        print(f"❌ 策略初始化测试失败：{str(e)}")


def test_simple_trading():
    """测试简化交易逻辑"""
    print("\n=== 测试简化交易逻辑 ===")
    try:
        # 创建模拟ContextInfo
        context = MockContextInfo()

        # 初始化策略
        strategy.init_database()
        strategy.load_strategy_status()

        # 模拟信号信息
        mock_signal = {
            'signal_type': 'ENTERLONG',
            'signal_date': '2024-01-01',
            'signal_price': 2.5
        }

        # 测试激活期交易
        print("测试激活期交易...")
        strategy.execute_simple_active_trade(context, mock_signal)
        print("✅ 激活期交易测试完成")

        # 测试沉睡期交易
        print("测试沉睡期交易...")
        mock_signal['signal_type'] = 'EXITLONG'
        strategy.execute_simple_sleeping_trade(context, mock_signal)
        print("✅ 沉睡期交易测试完成")

    except Exception as e:
        print(f"❌ 简化交易测试失败：{str(e)}")


def test_phase_detection():
    """测试阶段判断逻辑"""
    print("\n=== 测试阶段判断逻辑 ===")
    try:
        # 创建模拟ContextInfo
        context = MockContextInfo()

        # 初始化策略
        strategy.init_database()
        strategy.load_strategy_status()

        # 测试没有历史信号时的默认阶段（会触发历史扫描）
        print("测试默认阶段判断（会触发历史扫描）...")
        result = strategy.detect_latest_signal(context)
        if result:
            print(f"✅ 扫描后阶段：{result['current_phase']}")
        else:
            print("❌ 阶段检测失败")

        # 测试历史信号记录功能
        print("测试历史信号记录...")
        mock_historical_signal = {
            'signal_type': 'ENTERLONG',
            'signal_date': '2024-06-28',
            'signal_price': 2.5,
            'ema_value': 2.0,
            'bottom_line': 1.8,
            'top_line': None
        }
        strategy.record_historical_signal_to_db(mock_historical_signal)

        # 再次测试阶段判断
        result = strategy.detect_latest_signal(context)
        if result:
            print(f"✅ 历史买入信号后阶段：{result['current_phase']}")
        else:
            print("❌ 阶段检测失败")

        # 测试INSERT OR REPLACE功能（重复插入同一日期的信号）
        print("测试INSERT OR REPLACE功能...")
        mock_historical_signal['signal_price'] = 2.6  # 修改价格
        strategy.record_historical_signal_to_db(mock_historical_signal)
        print("✅ 重复插入测试完成")

    except Exception as e:
        print(f"❌ 阶段判断测试失败：{str(e)}")


def test_historical_scan():
    """测试历史扫描功能"""
    print("\n=== 测试历史扫描功能 ===")
    try:
        # 创建模拟ContextInfo
        context = MockContextInfo()

        # 初始化策略
        strategy.init_database()
        strategy.load_strategy_status()

        # 直接测试历史扫描函数
        print("开始历史扫描测试...")
        strategy.scan_and_record_all_historical_signals(context)
        print("✅ 历史扫描测试完成")

        # 查询扫描结果
        latest_signal = strategy.get_latest_signal_from_db()
        if latest_signal:
            print(f"✅ 扫描到最新信号：{latest_signal['signal_type']}，日期：{latest_signal.get('signal_date', 'unknown')}")
        else:
            print("⚠️ 扫描后没有发现信号")

    except Exception as e:
        print(f"❌ 历史扫描测试失败：{str(e)}")


def run_all_tests():
    """运行所有测试"""
    print("开始测试激进版策略...")
    
    test_database_initialization()
    test_technical_indicators()
    test_signal_detection()
    test_strategy_initialization()
    test_simple_trading()
    test_phase_detection()
    test_historical_scan()
    
    print("\n=== 测试完成 ===")
    
    # 清理测试文件
    try:
        if os.path.exists("test_aggressive_strategy.db"):
            os.remove("test_aggressive_strategy.db")
            print("✅ 测试数据库文件已清理")
    except:
        pass


if __name__ == "__main__":
    run_all_tests()
